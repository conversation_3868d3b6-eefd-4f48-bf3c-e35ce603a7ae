<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .products-section {
            min-height: 100vh;
            background: #f8f9fa;
            position: relative;
            padding: 80px 0;
        }

        @keyframes float {
            0%, 100% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(10px) translateY(-10px); }
            50% { transform: translateX(-5px) translateY(5px); }
            75% { transform: translateX(5px) translateY(-5px); }
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
            position: relative;
            z-index: 2;
        }

        .section-title {
            font-size: clamp(2rem, 4vw, 3.5rem);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 1rem;
            color: #1a202c;
        }

        .section-title .highlight {
            background: linear-gradient(45deg, #ff8f00, #ff6f00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #2d3748;
            line-height: 1.6;
            max-width: 600px;
            margin: 0 auto;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .product-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }



        .product-name {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 12px;
            line-height: 1.2;
        }

        .product-description {
            color: #2d3748;
            line-height: 1.6;
            margin-bottom: 20px;
            font-size: 0.95rem;
            text-align: justify;
        }

        .product-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .info-item {
            background: rgba(255, 143, 0, 0.1);
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid rgba(255, 143, 0, 0.2);
        }

        .info-label {
            font-size: 0.9rem;
            color: #2d3748;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .info-value {
            font-size: 1.3rem;
            font-weight: 700;
            color: #ff6f00;
        }

        .product-features {
            margin-bottom: 25px;
        }

        .features-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 12px;
        }

        .features-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .feature-tag {
            background: rgba(26, 32, 44, 0.1);
            color: #1a202c;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            border: 1px solid rgba(26, 32, 44, 0.1);
            transition: all 0.2s ease;
        }

        .feature-tag:hover {
            background: rgba(26, 32, 44, 0.15);
            transform: translateY(-1px);
        }

        .btn-view-gallery {
            margin-top: auto;
            width: 100%;
            background: linear-gradient(45deg, #1a202c, #2d3748);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-view-gallery::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-view-gallery:hover::before {
            left: 100%;
        }

        .btn-view-gallery:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 32, 44, 0.3);
        }

        /* Modal Styles - Tối ưu */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(8px);
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            position: relative;
            background-color: white;
            margin: 3% auto;
            padding: 0;
            width: 92%;
            max-width: 900px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
            animation: slideUp 0.4s ease;
        }

        @keyframes slideUp {
            from { 
                transform: translateY(50px);
                opacity: 0;
            }
            to { 
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            padding: 25px 30px;
            background: linear-gradient(45deg, #1a202c, #2d3748);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.6rem;
            font-weight: 700;
            margin: 0;
        }

        .close {
            color: white;
            font-size: 30px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close:hover {
            color: #ff8f00;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(90deg);
        }

        .slider-container {
            position: relative;
            overflow: hidden;
            background: #f8f9fa;
        }

        .slider-wrapper {
            display: flex;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .slide {
            min-width: 100%;
            position: relative;
            background: white;
        }

        .slide img {
            width: 100%;
            height: 450px;
            object-fit: contain;
            background: #f8f9fa;
            display: block;
        }

        .slide-info {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 40%, transparent 100%);
            color: white;
            padding: 20px 25px 40px;
            backdrop-filter: blur(8px);
        }

        .slide-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
            background: rgba(0, 0, 0, 0.5);
            padding: 10px 15px;
            border-radius: 8px;
            display: inline-block;
        }

        .slider-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.95);
            border: none;
            width: 55px;
            height: 55px;
            border-radius: 50%;
            font-size: 1.4rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
            z-index: 10;
        }

        .slider-nav:hover {
            background: white;
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .prev {
            left: 25px;
        }

        .next {
            right: 25px;
        }

        .slider-dots {
            display: flex;
            justify-content: center;
            gap: 12px;
            padding: 25px;
            background: rgba(248, 250, 252, 0.98);
            backdrop-filter: blur(10px);
        }

        .dot {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: #cbd5e0;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .dot.active {
            background: #ff8f00;
            transform: scale(1.3);
            border-color: rgba(255, 143, 0, 0.3);
            box-shadow: 0 0 0 4px rgba(255, 143, 0, 0.2);
        }

        .dot:hover {
            background: #a0aec0;
            transform: scale(1.1);
        }

        /* Mobile Responsive - Cải thiện */
        @media (max-width: 768px) {
            .products-grid {
                grid-template-columns: 1fr;
                gap: 30px;
                padding: 0 20px;
            }

            .product-card {
                padding: 20px;
            }



            .modal-content {
                width: 95%;
                margin: 5% auto;
            }

            .modal-header {
                padding: 20px;
            }

            .modal-title {
                font-size: 1.3rem;
            }

            .slide img {
                height: 350px;
            }

            .slider-nav {
                width: 45px;
                height: 45px;
                font-size: 1.2rem;
            }

            .prev {
                left: 15px;
            }

            .next {
                right: 15px;
            }

            .slide-info {
                padding: 15px 20px 30px;
            }

            .slide-title {
                font-size: 1.2rem;
                padding: 8px 12px;
            }
        }

        @media (max-width: 480px) {
            .products-section {
                padding: 60px 0;
            }

            .section-header {
                margin-bottom: 3rem;
            }

            .product-info {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .slide img {
                height: 280px;
            }

            .slider-nav {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .prev {
                left: 10px;
            }

            .next {
                right: 10px;
            }
        }
    </style>
</head>
<body>
    <section class="products-section">
        <div class="container">
            <div class="section-header">
                <h1 class="section-title">
                    Sản Phẩm <span class="highlight">Chất Lượng Cao</span> Từ Loma
                </h1>
                <p class="section-subtitle">
                    Khám phá bộ sưu tập túi vải đa dạng với chất liệu cao cấp, 
                    thiết kế hiện đại và mức giá cạnh tranh nhất thị trường
                </p>
            </div>

            <div class="products-grid">
                <!-- Túi Vải Canvas -->
                <div class="product-card" onclick="openModal('canvas')">
                    <div style="width: 100%; height: 0; padding-bottom: 100%; object-fit: cover; border-radius: 15px; margin-bottom: 20px; transition: transform 0.3s ease; aspect-ratio: 1 / 1; background-size: cover; background-position: center; background-repeat: no-repeat; position: relative;">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-canvas-hop.png" 
                             alt="Túi Vải Canvas"
                             style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; border-radius: 15px;">
                    </div>
                    
                    <h3 class="product-name">Túi Vải Canvas</h3>
                    <p class="product-description">
                        Vải canvas thông dụng, đa dạng để tạo túi form mềm như túi tote phổ biến, 
                        hoặc có thể ép thêm lớp vải không dệt dày để làm form túi hộp cứng cáp. 
                        Đa dạng màu sắc, phù hợp đặt số lượng lớn với mức giá tối ưu.
                    </p>

                    <div class="product-info">
                        <div class="info-item">
                            <div class="info-label">Số lượng tối thiểu</div>
                            <div class="info-value">50 túi</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Mức giá từ</div>
                            <div class="info-value">25.000đ</div>
                        </div>
                    </div>

                    <div class="product-features">
                        <div class="features-title">Đặc điểm nổi bật:</div>
                        <div class="features-list">
                            <span class="feature-tag">Thân thiện môi trường</span>
                            <span class="feature-tag">Giá cạnh tranh</span>
                            <span class="feature-tag">Thông dụng trên thị trường</span>
                            <span class="feature-tag">Đa dạng form dáng</span>
                            <span class="feature-tag">Phù hợp đặt số lượng lớn</span>
                        </div>
                    </div>

                    <button class="btn-view-gallery">
                        🖼️ Xem Thư Viện Ảnh
                    </button>
                </div>

                <!-- Túi Vải Đay -->
                <div class="product-card" onclick="openModal('day')">
                    <div style="width: 100%; height: 0; padding-bottom: 100%; object-fit: cover; border-radius: 15px; margin-bottom: 20px; transition: transform 0.3s ease; aspect-ratio: 1 / 1; background-size: cover; background-position: center; background-repeat: no-repeat; position: relative;">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-day-in-logo/tui-vai-day-linen-in-chuyen-nhiet-full-tui-loma-bag-1.png" 
                             alt="Túi Vải Đay"
                             style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; border-radius: 15px;">
                    </div>
                    
                    <h3 class="product-name">Túi Vải Đay</h3>
                    <p class="product-description">
                        Vải đay linen cao cấp với chất liệu tự nhiên 100%, trendy và phù hợp với thời trang hiện đại. 
                        Form đứng cứng cáp, tạo ấn tượng sang trọng cho thương hiệu high-end. 
                        Đầu tư xứng đáng cho những đơn hàng số lượng lớn cần chất lượng cao.
                    </p>

                    <div class="product-info">
                        <div class="info-item">
                            <div class="info-label">Số lượng tối thiểu</div>
                            <div class="info-value">50 túi</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Mức giá từ</div>
                            <div class="info-value">45.000đ</div>
                        </div>
                    </div>

                    <div class="product-features">
                        <div class="features-title">Đặc điểm nổi bật:</div>
                        <div class="features-list">
                            <span class="feature-tag">Cao cấp</span>
                            <span class="feature-tag">Sang trọng</span>
                            <span class="feature-tag">Form đứng cứng cáp</span>
                            <span class="feature-tag">Trendy & thời trang</span>
                            <span class="feature-tag">Phù hợp thương hiệu high-end</span>
                        </div>
                    </div>

                    <button class="btn-view-gallery">
                        🖼️ Xem Thư Viện Ảnh
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Modal Canvas -->
    <div id="canvasModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" style="color: #fff;">Thư Viện Ảnh - Túi Vải Canvas</h2>
                <span class="close" onclick="closeModal('canvas')">&times;</span>
            </div>
            <div class="slider-container">
                <div class="slider-wrapper" id="canvasSlider">
                    <div class="slide">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/kich-thuoc-tui-vai-canvas-loma-bag.png" alt="Canvas 1">
                    </div>
                    <div class="slide">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-vai-canva-chan-day-loma-bag-in-logo-theo-yeu-cau.png" alt="Canvas 2">
                    </div>
                    <div class="slide">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-06/tui-canvas-jewelry2.png" alt="Canvas 3">
                    </div>
                    <div class="slide">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-08/tui-canvas-dep1.png" alt="Canvas 4">
                    </div>
                    <div class="slide">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/hinh-tui-loma/sp-08/tui-canvas-dep2.png" alt="Canvas 5">
                    </div>
                </div>
                <button class="slider-nav prev" onclick="changeSlide('canvas', -1)">❮</button>
                <button class="slider-nav next" onclick="changeSlide('canvas', 1)">❯</button>
            </div>
            <div class="slider-dots" id="canvasDots">
                <span class="dot active" onclick="currentSlide('canvas', 1)"></span>
                <span class="dot" onclick="currentSlide('canvas', 2)"></span>
                <span class="dot" onclick="currentSlide('canvas', 3)"></span>
                <span class="dot" onclick="currentSlide('canvas', 4)"></span>
                <span class="dot" onclick="currentSlide('canvas', 5)"></span>
            </div>
        </div>
    </div>

    <!-- Modal Đay -->
    <div id="dayModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Thư Viện Ảnh - Túi Vải Đay</h2>
                <span class="close" onclick="closeModal('day')">&times;</span>
            </div>
            <div class="slider-container">
                <div class="slider-wrapper" id="daySlider">
                    <div class="slide">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-day-in-logo/tui-vai-day-linen-in-chuyen-nhiet-full-tui-loma-bag-2-mau-hong.png" alt="Đay 1">
                    </div>
                    <div class="slide">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/landing-page/tui-day-in-logo/tui-vai-day-linen-in-nhieu-mau-loma-bag.png" alt="Đay 2">
                    </div>
                    <div class="slide">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/mockup/tui-day-mau-sac/12.png" alt="Đay 3">
                    </div>
                    <div class="slide">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/mockup/tui-day-mau-sac/14.png" alt="Đay 4">
                    </div>
                    <div class="slide">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/mockup/tui-day-mau-sac/4.png" alt="Đay 5">
                    </div>
                </div>
                <button class="slider-nav prev" onclick="changeSlide('day', -1)">❮</button>
                <button class="slider-nav next" onclick="changeSlide('day', 1)">❯</button>
            </div>
            <div class="slider-dots" id="dayDots">
                <span class="dot active" onclick="currentSlide('day', 1)"></span>
                <span class="dot" onclick="currentSlide('day', 2)"></span>
                <span class="dot" onclick="currentSlide('day', 3)"></span>
                <span class="dot" onclick="currentSlide('day', 4)"></span>
                <span class="dot" onclick="currentSlide('day', 5)"></span>
            </div>
        </div>
    </div>

    <script>
        let slideIndexes = {
            canvas: 1,
            day: 1
        };

        function openModal(type) {
            document.getElementById(type + 'Modal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal(type) {
            document.getElementById(type + 'Modal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function changeSlide(type, direction) {
            const slider = document.getElementById(type + 'Slider');
            const slides = slider.children;
            const totalSlides = slides.length;
            
            slideIndexes[type] += direction;
            
            if (slideIndexes[type] > totalSlides) {
                slideIndexes[type] = 1;
            }
            if (slideIndexes[type] < 1) {
                slideIndexes[type] = totalSlides;
            }
            
            updateSlider(type);
        }

        function currentSlide(type, index) {
            slideIndexes[type] = index;
            updateSlider(type);
        }

        function updateSlider(type) {
            const slider = document.getElementById(type + 'Slider');
            const dots = document.getElementById(type + 'Dots').children;
            
            const translateX = -(slideIndexes[type] - 1) * 100;
            slider.style.transform = `translateX(${translateX}%)`;
            
            // Update dots
            Array.from(dots).forEach((dot, index) => {
                if (index === slideIndexes[type] - 1) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const canvasModal = document.getElementById('canvasModal');
            const dayModal = document.getElementById('dayModal');
            
            if (event.target === canvasModal) {
                closeModal('canvas');
            }
            if (event.target === dayModal) {
                closeModal('day');
            }
        }

        // Touch events for mobile
        let startX = 0;
        let currentModal = null;

        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            if (document.getElementById('canvasModal').style.display === 'block') {
                currentModal = 'canvas';
            } else if (document.getElementById('dayModal').style.display === 'block') {
                currentModal = 'day';
            }
        });

        document.addEventListener('touchend', function(e) {
            if (!currentModal) return;
            
            const endX = e.changedTouches[0].clientX;
            const diff = startX - endX;
            
            if (Math.abs(diff) > 50) { // Minimum swipe distance
                if (diff > 0) {
                    changeSlide(currentModal, 1); // Swipe left - next slide
                } else {
                    changeSlide(currentModal, -1); // Swipe right - previous slide
                }
            }
            
            currentModal = null;
        });

        // Auto-play slider (optional)
        setInterval(() => {
            const canvasModal = document.getElementById('canvasModal');
            const dayModal = document.getElementById('dayModal');
            
            if (canvasModal.style.display === 'block') {
                changeSlide('canvas', 1);
            }
            if (dayModal.style.display === 'block') {
                changeSlide('day', 1);
            }
        }, 5000);

        // Add entrance animations
        window.addEventListener('load', function() {
            const productCards = document.querySelectorAll('.product-card');
            
            productCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.8s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 200 + (index * 200));
            });
        });
    </script>
</body>
</html>
