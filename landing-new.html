<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loma Bag - Gi<PERSON>i <PERSON>áp Túi <PERSON>p Tăng Tỷ Lệ <PERSON></title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@400;500;700;800&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- SwiperJS for Gallery Slider -->
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />

    <style>
        /* Custom Styles */
        body {
            font-family: 'Be Vietnam Pro', sans-serif;
            background-color: #f9fafb; /* gray-50 */
            line-height: 1.7; /* Tăng khoảng cách dòng để dễ đọc tiếng Việt */
        }
        h1, h2, h3, h4, h5, h6 {
            line-height: 1.4; /* Tăng khoảng cách dòng cho tiêu đề */
        }
        .cta-button {
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .cta-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4); /* red-500 */
        }
        .section-title {
            position: relative;
            padding-bottom: 1rem;
        }
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: #EF4444; /* red-500 */
            border-radius: 2px;
        }
        /* Typing animation */
        .typed-cursor {
            opacity: 1;
            animation: blink 0.7s infinite;
        }
        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0; }
            100% { opacity: 1; }
        }
        /* Modal */
        .modal {
            transition: opacity 0.3s ease;
        }
        .video-placeholder {
            aspect-ratio: 16 / 9;
            background-color: #111827; /* gray-900 */
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.75rem;
            cursor: pointer;
            overflow: hidden;
            position: relative;
        }
        .play-button {
            width: 80px;
            height: 80px;
            background-color: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            border: 2px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease, background-color 0.2s ease;
            z-index: 10;
        }
        .play-button:hover {
            transform: scale(1.1);
            background-color: rgba(239, 68, 68, 0.8);
        }
        .video-placeholder img {
            transition: transform 0.5s ease;
        }
        .video-placeholder:hover img {
            transform: scale(1.05);
        }
        /* Calculator Tool Custom Range Input */
        .calculator-tool input[type="range"] {
            -webkit-appearance: none; appearance: none;
            width: 100%; height: 8px; background: #d1d5db;
            border-radius: 5px; outline: none; opacity: 0.7;
            transition: opacity .2s;
        }
        .calculator-tool input[type="range"]:hover { opacity: 1; }
        .calculator-tool input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none; appearance: none;
            width: 24px; height: 24px; background: #EF4444;
            cursor: pointer; border-radius: 50%;
        }
        .calculator-tool input[type="range"]::-moz-range-thumb {
            width: 24px; height: 24px; background: #EF4444;
            cursor: pointer; border-radius: 50%;
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- HEADER -->
    <header class="bg-white/90 backdrop-blur-lg fixed top-0 left-0 right-0 z-50 shadow-sm">
        <div class="container mx-auto px-6 py-4 flex justify-between items-center">
            <h1 class="text-2xl font-bold text-gray-800">Loma<span class="text-red-500">Bag</span></h1>
            <a href="#form-bao-gia" class="cta-button bg-red-500 text-white font-bold py-2 px-6 rounded-full hover:bg-red-600 hidden sm:inline-block">
                Tư Vấn Miễn Phí
            </a>
        </div>
    </header>

    <!-- HERO SECTION -->
    <section class="bg-white text-gray-800 pt-32 pb-20 text-center">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl md:text-5xl font-extrabold leading-tight mb-4">
                Đang chạy Ads? Thêm quà tặng để... <br>
                <span id="typing-effect" class="text-red-500"></span>
            </h2>
            <p class="text-lg md:text-xl mb-8 max-w-3xl mx-auto text-gray-600">
                Bạn đã tốn tiền để khách bấm vào quảng cáo. Đừng để họ thoát ra! 
                Một chiếc túi vải in logo sẽ là "cú hích" cuối cùng để khách hàng chốt đơn ngay lập tức.
            </p>
            <div class="flex flex-wrap justify-center items-center gap-4">
                 <a href="#calculator-tool" class="cta-button bg-red-500 text-white font-bold py-4 px-10 rounded-full text-xl hover:bg-red-600 inline-block">
                    Tính Thử Lợi Nhuận
                </a>
                <a href="#case-study" class="cta-button bg-gray-700 text-white font-bold py-4 px-10 rounded-full text-xl hover:bg-gray-800 inline-block">
                    Xem Case Study
                </a>
            </div>
        </div>
    </section>

    <!-- NEW CALCULATOR TOOL SECTION -->
    <section id="calculator-tool" class="py-16 md:py-24 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h3 class="section-title text-3xl md:text-4xl font-bold">Công Cụ Tính Toán Lợi Nhuận Thực Tế</h3>
                <p class="mt-4 text-gray-600 max-w-3xl mx-auto">Nhập số liệu kinh doanh của bạn và kéo thanh trượt để xem việc tặng túi ảnh hưởng đến lợi nhuận như thế nào.</p>
            </div>
            <div class="calculator-tool max-w-6xl mx-auto bg-white p-6 sm:p-8 rounded-xl shadow-2xl grid lg:grid-cols-2 gap-8 lg:gap-12">
                <!-- Inputs -->
                <div class="space-y-6">
                    <h4 class="text-2xl font-bold text-gray-800 border-b pb-2">1. Thông số hiện tại</h4>
                    <div>
                        <label for="currentRevenue" class="font-semibold">Doanh thu hiện tại/tháng (VND)</label>
                        <input type="number" id="currentRevenue" value="50000000" class="mt-1 w-full p-3 border border-gray-300 rounded-md text-lg font-bold focus:ring-2 focus:ring-red-500">
                    </div>
                    <div>
                        <label for="currentAOV" class="font-semibold">Giá trị đơn hàng trung bình (VND)</label>
                        <input type="number" id="currentAOV" value="400000" class="mt-1 w-full p-3 border border-gray-300 rounded-md text-lg font-bold focus:ring-2 focus:ring-red-500">
                    </div>
                     <div>
                        <label for="adSpend" class="font-semibold">Chi phí quảng cáo/tháng (VND)</label>
                        <input type="number" id="adSpend" value="10000000" class="mt-1 w-full p-3 border border-gray-300 rounded-md text-lg font-bold focus:ring-2 focus:ring-red-500">
                    </div>

                    <h4 class="text-2xl font-bold text-gray-800 border-b pb-2 pt-4">2. Thiết lập kịch bản</h4>
                     <div>
                        <label for="conversionRateIncrease" class="font-semibold">Ước tính Tỷ lệ chốt đơn tăng</label>
                        <div class="flex items-center mt-1">
                            <input type="range" id="conversionRateIncrease" min="0" max="50" value="15" class="w-full">
                            <span id="conversionRateIncreaseValue" class="ml-4 text-lg font-bold w-16 text-center">15%</span>
                        </div>
                    </div>
                    <div>
                        <label for="aovIncrease" class="font-semibold">Ước tính Giá trị đơn hàng tăng</label>
                        <div class="flex items-center mt-1">
                            <input type="range" id="aovIncrease" min="0" max="50" value="10" class="w-full">
                            <span id="aovIncreaseValue" class="ml-4 text-lg font-bold w-16 text-center">10%</span>
                        </div>
                    </div>
                    <div>
                        <label for="bagCostPercentage" class="font-semibold">Ngân sách cho 1 túi quà (% trên AOV mới)</label>
                        <div class="flex items-center mt-1">
                            <input type="range" id="bagCostPercentage" min="3" max="15" value="8" class="w-full">
                            <span id="bagCostPercentageValue" class="ml-4 text-lg font-bold w-16 text-center">8%</span>
                        </div>
                    </div>
                </div>
                <!-- Outputs -->
                <div class="bg-red-50/50 p-6 rounded-lg">
                    <h4 class="text-2xl font-bold text-gray-800 mb-4">3. Kết quả dự kiến</h4>
                    <div class="space-y-4">
                        <div class="p-3 bg-gray-200/50 rounded-md">
                            <p class="text-sm font-semibold text-gray-600">Số đơn hàng hiện tại / tháng</p>
                            <p id="currentOrders" class="text-xl font-bold text-gray-800">125 đơn</p>
                        </div>
                        <div class="p-3 bg-gray-200/50 rounded-md">
                            <p class="text-sm font-semibold text-gray-600">Lợi nhuận hiện tại (trước COGS)</p>
                            <p id="currentProfit" class="text-xl font-bold text-gray-800">40.000.000đ</p>
                        </div>
                        <hr class="my-4 border-dashed">
                        <div class="p-3 bg-white rounded-md shadow-sm">
                            <p class="text-sm font-semibold text-red-700">Chi phí cho 1 túi quà</p>
                            <p id="costPerBag" class="text-xl font-bold text-red-600">35.200đ</p>
                        </div>
                        <div class="p-3 bg-white rounded-md shadow-sm">
                            <p class="text-sm font-semibold text-gray-600">Số đơn hàng mới / tháng</p>
                            <p id="newOrders" class="text-xl font-bold text-gray-800">144 đơn</p>
                        </div>
                        <div class="p-3 bg-white rounded-md shadow-sm">
                            <p class="text-sm font-semibold text-gray-600">Tổng chi phí túi / tháng</p>
                            <p id="totalBagCost" class="text-xl font-bold text-orange-600">- 5.071.000đ</p>
                        </div>
                    </div>
                    <div class="mt-6 bg-green-100 border-2 border-green-500 text-green-800 p-4 rounded-lg text-center">
                        <p class="font-semibold uppercase">Lợi nhuận tăng thêm</p>
                        <p id="profitIncrease" class="text-4xl font-extrabold text-green-600 mt-1">11.609.000đ</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- CASE STUDY SECTION -->
    <section id="case-study" class="py-16 md:py-24 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h3 class="section-title text-3xl md:text-4xl font-bold">Người Thật, Việc Thật: Túi Vải Đã Hiệu Quả Ra Sao?</h3>
                <p class="mt-4 text-gray-600 max-w-2xl mx-auto">Không chỉ là lời nói suông, đây là kết quả khách hàng của chúng tôi đã đạt được.</p>
            </div>
            <div class="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                <div class="bg-gray-50 p-8 rounded-lg shadow-lg flex flex-col">
                    <p class="text-lg font-semibold text-red-600">Khách hàng ngành mỹ phẩm</p>
                    <p class="text-3xl md:text-4xl font-extrabold text-gray-800 my-4">Hơn 20.000 túi</p>
                    <p class="text-gray-600 flex-grow">"Ban đầu chỉ dám đặt thử 2.000 túi, không ngờ tặng hết veo trong 15 ngày. Khách hàng cực kỳ thích món quà này, liên tục hỏi và quay lại mua. Giờ đây túi vải là một phần không thể thiếu trong các chiến dịch của shop."</p>
                    <div class="mt-6 pt-4 border-t border-gray-200">
                        <span class="inline-block bg-red-100 text-red-800 text-sm font-semibold mr-2 px-2.5 py-0.5 rounded-full">Kích thích mua hàng</span>
                        <span class="inline-block bg-green-100 text-green-800 text-sm font-semibold mr-2 px-2.5 py-0.5 rounded-full">Tăng khách hàng trung thành</span>
                    </div>
                </div>
                <div class="bg-gray-50 p-8 rounded-lg shadow-lg flex flex-col">
                    <p class="text-lg font-semibold text-red-600">Khách hàng ngành thời trang</p>
                    <p class="text-3xl md:text-4xl font-extrabold text-gray-800 my-4">5.000 túi / tháng</p>
                    <p class="text-gray-600 flex-grow">"Chương trình tặng túi cho đơn hàng từ 500k giúp AOV (giá trị đơn trung bình) của shop tăng rõ rệt. Khách sẵn sàng mua thêm đồ để nhận được túi. Mỗi tháng đều đặn đặt 5.000 túi và luôn trong tình trạng 'cháy hàng'."</p>
                    <div class="mt-6 pt-4 border-t border-gray-200">
                        <span class="inline-block bg-blue-100 text-blue-800 text-sm font-semibold mr-2 px-2.5 py-0.5 rounded-full">Tăng giá trị đơn hàng</span>
                        <span class="inline-block bg-yellow-100 text-yellow-800 text-sm font-semibold mr-2 px-2.5 py-0.5 rounded-full">Marketing 0 đồng</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ABOUT FACTORY SECTION -->
    <section class="py-16 md:py-24 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h3 class="section-title text-3xl md:text-4xl font-bold">Xưởng Sản Xuất Loma Bag</h3>
                <p class="mt-4 text-gray-600 max-w-2xl mx-auto">Sản xuất trực tiếp - không qua trung gian. Đảm bảo chất lượng và giá thành tốt nhất cho bạn.</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <h4 class="font-bold text-xl mb-2">Máy Móc Hiện Đại</h4>
                    <p class="text-gray-600">Hệ thống máy may, máy in công nghệ cao, đảm bảo sản phẩm đồng đều, đường may chắc chắn, hình in sắc nét.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <h4 class="font-bold text-xl mb-2">Đội Ngũ Lành Nghề</h4>
                    <p class="text-gray-600">Thợ may, thợ in có kinh nghiệm lâu năm, tỉ mỉ trong từng công đoạn để tạo ra sản phẩm hoàn hảo nhất.</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <h4 class="font-bold text-xl mb-2">Kiểm Soát Chất Lượng</h4>
                    <p class="text-gray-600">Quy trình KCS (Kiểm tra chất lượng sản phẩm) nghiêm ngặt từ khâu chọn vải, cắt, in, may cho đến đóng gói.</p>
                </div>
                 <div class="bg-white p-6 rounded-lg shadow-md text-center">
                    <h4 class="font-bold text-xl mb-2">Năng Lực Sản Xuất Lớn</h4>
                    <p class="text-gray-600">Sẵn sàng đáp ứng các đơn hàng từ số lượng nhỏ 50 túi đến hàng chục nghìn túi, đảm bảo tiến độ giao hàng.</p>
                </div>
            </div>
             <div class="text-center mt-12">
                <a href="#video-section" class="cta-button bg-white text-gray-800 border border-gray-300 font-bold py-3 px-8 rounded-full hover:bg-gray-100">
                    Xem Video Xưởng Sản Xuất
                </a>
            </div>
        </div>
    </section>

    <!-- VIDEO SECTION -->
    <section id="video-section" class="py-16 md:py-24 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h3 class="section-title text-3xl md:text-4xl font-bold">Trăm Nghe Không Bằng Một Thấy</h3>
                <p class="mt-4 text-gray-600 max-w-2xl mx-auto">Xem video để cảm nhận chất lượng sản phẩm và sự chuyên nghiệp của Loma Bag.</p>
            </div>
            <div class="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
                <div>
                    <div class="video-placeholder shadow-xl" data-video-id="dQw4w9WgXcQ">
                        <img src="https://placehold.co/1280x720/EFE8DE/333333?text=Cac+Mau+Tui+Hot+Nhat" alt="Các mẫu túi hot nhất" class="absolute inset-0 w-full h-full object-cover">
                        <div class="play-button">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg>
                        </div>
                    </div>
                    <h4 class="text-center text-xl font-bold mt-4">Video Giới Thiệu Các Mẫu Túi</h4>
                </div>
                <div>
                    <div class="video-placeholder shadow-xl" data-video-id="dQw4w9WgXcQ">
                         <img src="https://placehold.co/1280x720/D4E4F3/333333?text=Tham+Quan+Xuong+Loma+Bag" alt="Tham quan xưởng Loma Bag" class="absolute inset-0 w-full h-full object-cover">
                        <div class="play-button">
                             <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg>
                        </div>
                    </div>
                    <h4 class="text-center text-xl font-bold mt-4">Video Tham Quan Xưởng Sản Xuất</h4>
                </div>
            </div>
        </div>
    </section>

    <!-- PRICING & PLANS SECTION -->
    <section class="py-16 md:py-24 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h3 class="section-title text-3xl md:text-4xl font-bold">Bảng Giá & Phương Án Triển Khai</h3>
                <p class="mt-4 text-gray-600 max-w-2xl mx-auto">Chúng tôi có các gói linh hoạt để bạn bắt đầu một cách an toàn và hiệu quả nhất.</p>
            </div>
            <div class="text-center mb-12 bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
                <h4 class="text-xl font-semibold">Mức giá tham khảo</h4>
                <p class="text-5xl font-extrabold my-2 text-red-600">25.000đ - 90.000đ<span class="text-lg font-normal text-gray-500">/túi</span></p>
                <p class="text-gray-500">Giá chính xác phụ thuộc vào: Số lượng, Mẫu túi, Kích thước, Hình in...</p>
            </div>
            <div class="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                <div class="bg-white p-8 rounded-lg shadow-lg border-2 border-red-500 flex flex-col">
                    <h4 class="text-2xl font-bold text-red-600">Gói 1: Thử Nghiệm An Toàn</h4>
                    <p class="text-gray-600 mt-2 mb-4">Dành cho bạn muốn đo lường hiệu quả trước khi đầu tư lớn.</p>
                    <ul class="space-y-3 text-gray-700 flex-grow">
                        <li class="flex items-start"><span class="text-green-500 font-bold mr-2">✔</span> <strong>Miễn phí thiết kế Mockup:</strong> Xem trước mẫu túi với logo của bạn hoàn toàn miễn phí.</li>
                        <li class="flex items-start"><span class="text-green-500 font-bold mr-2">✔</span> <strong>Làm 1 túi mẫu thực tế:</strong> Chỉ cần cọc 500k. Dùng túi mẫu để quay video, chụp ảnh, xem chất lượng.</li>
                        <li class="flex items-start"><span class="text-green-500 font-bold mr-2">✔</span> <strong>Hoàn cọc 100%:</strong> Số tiền cọc 500k sẽ được hoàn lại toàn bộ khi bạn đặt đơn hàng từ 100 túi.</li>
                        <li class="flex items-start"><span class="text-green-500 font-bold mr-2">✔</span> <strong>Bắt đầu chỉ từ 50 túi:</strong> Đặt đơn hàng đầu tiên với số lượng nhỏ để giảm thiểu rủi ro.</li>
                    </ul>
                    <a href="#form-bao-gia" class="cta-button mt-6 w-full text-center bg-red-500 text-white font-bold py-3 px-6 rounded-full hover:bg-red-600">
                        Yêu Cầu Làm Mẫu Miễn Phí
                    </a>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-lg border-2 border-gray-300 flex flex-col">
                    <h4 class="text-2xl font-bold text-gray-800">Gói 2: Tối Ưu Chi Phí</h4>
                    <p class="text-gray-600 mt-2 mb-4">Dành cho shop đã có lượng đơn hàng ổn định, muốn có giá tốt nhất.</p>
                    <ul class="space-y-3 text-gray-700 flex-grow">
                        <li class="flex items-start"><span class="text-green-500 font-bold mr-2">✔</span> <strong>Giá gốc tại xưởng:</strong> Đặt số lượng càng lớn, giá trên mỗi túi càng rẻ.</li>
                        <li class="flex items-start"><span class="text-green-500 font-bold mr-2">✔</span> <strong>Ưu tiên sản xuất:</strong> Đơn hàng của bạn sẽ được ưu tiên xử lý nhanh chóng.</li>
                         <li class="flex items-start"><span class="text-green-500 font-bold mr-2">✔</span> <strong>Miễn phí giao hàng toàn quốc:</strong> Tiết kiệm thêm một khoản chi phí đáng kể.</li>
                        <li class="flex items-start"><span class="text-green-500 font-bold mr-2">✔</span> <strong>Tư vấn chiến lược:</strong> Hỗ trợ tư vấn các mẫu túi "best-seller" phù hợp với ngành hàng của bạn.</li>
                    </ul>
                     <a href="#form-bao-gia" class="cta-button mt-6 w-full text-center bg-gray-700 text-white font-bold py-3 px-6 rounded-full hover:bg-gray-800">
                        Nhận Báo Giá Sỉ
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- GALLERY SECTION -->
    <section class="py-16 md:py-24 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h3 class="section-title text-3xl md:text-4xl font-bold">Thư Viện Mẫu Túi</h3>
                <p class="mt-4 text-gray-600 max-w-2xl mx-auto">Click vào từng loại để xem thêm nhiều hình ảnh và ý tưởng đã được thực hiện.</p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
                <!-- Gallery items will trigger the modal -->
                <div class="gallery-item group rounded-lg overflow-hidden shadow-md relative" data-category="tote">
                    <img src="https://placehold.co/400x500/EFE8DE/333333?text=Tui+Tote" alt="Túi tote cơ bản" class="w-full h-full object-cover">
                    <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <span class="text-white text-lg font-bold">Túi Tote</span>
                    </div>
                </div>
                <div class="gallery-item group rounded-lg overflow-hidden shadow-md relative" data-category="day-rut">
                    <img src="https://placehold.co/400x500/D9E3DA/333333?text=Tui+Day+Rut" alt="Túi dây rút" class="w-full h-full object-cover">
                     <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <span class="text-white text-lg font-bold">Túi Dây Rút</span>
                    </div>
                </div>
                <div class="gallery-item group rounded-lg overflow-hidden shadow-md relative" data-category="hop">
                    <img src="https://placehold.co/400x500/F4D6D6/333333?text=Tui+Hop" alt="Túi hộp" class="w-full h-full object-cover">
                     <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <span class="text-white text-lg font-bold">Túi Hộp</span>
                    </div>
                </div>
                <div class="gallery-item group rounded-lg overflow-hidden shadow-md relative" data-category="in-an">
                    <img src="https://placehold.co/400x500/D4E4F3/333333?text=Hinh+In" alt="Mẫu in" class="w-full h-full object-cover">
                     <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <span class="text-white text-lg font-bold">Chất Lượng In</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FORM SECTION -->
    <section id="form-bao-gia" class="py-16 md:py-24 bg-gray-800 text-white">
        <div class="container mx-auto px-6">
            <div class="max-w-2xl mx-auto">
                <div class="text-center mb-10">
                    <h3 class="text-3xl md:text-4xl font-bold">Bắt Đầu Chiến Dịch Của Bạn Ngay Hôm Nay!</h3>
                    <p class="mt-4 text-gray-300 max-w-2xl mx-auto">Để lại thông tin, Loma Bag sẽ liên hệ tư vấn miễn phí và gửi bạn thiết kế mockup đầu tiên!</p>
                </div>
                <form action="#" method="POST" class="space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-300">Tên của bạn</label>
                        <input type="text" name="name" id="name" required class="mt-1 block w-full px-4 py-3 rounded-md bg-gray-700 border-gray-600 focus:ring-red-500 focus:border-red-500 text-white">
                    </div>
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-300">Số Điện Thoại (để nhận tư vấn qua Zalo)</label>
                        <input type="tel" name="phone" id="phone" required class="mt-1 block w-full px-4 py-3 rounded-md bg-gray-700 border-gray-600 focus:ring-red-500 focus:border-red-500 text-white">
                    </div>
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-300">Bạn quan tâm đến Gói Thử Nghiệm hay Gói Tối Ưu?</label>
                        <input type="text" name="notes" id="notes" placeholder="Ví dụ: Gói Thử Nghiệm" class="mt-1 block w-full px-4 py-3 rounded-md bg-gray-700 border-gray-600 focus:ring-red-500 focus:border-red-500 text-white"></input>
                    </div>
                    <div class="text-center pt-4">
                        <button type="submit" class="cta-button w-full sm:w-auto bg-red-500 text-white font-bold py-4 px-16 rounded-full text-lg hover:bg-red-600">
                            GỬI YÊU CẦU TƯ VẤN
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- FOOTER -->
    <footer class="bg-gray-900 text-gray-400">
        <div class="container mx-auto px-6 py-8 text-center">
            <h3 class="text-xl font-bold text-white">Loma<span class="text-red-500">Bag</span></h3>
            <p class="mt-2">Giải pháp túi vải thực chiến cho người kinh doanh online.</p>
            <p class="mt-1">Hotline/Zalo: 09xx.xxx.xxx | Email: <EMAIL></p>
            <p class="mt-1">Địa chỉ: Quận 12, TP. Hồ Chí Minh</p>
            <p class="mt-4">&copy; 2024 Loma Bag. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- MODALS -->
    <div id="gallery-modal" class="modal fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden opacity-0">
        <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-2/3 max-w-4xl relative p-4">
            <button class="close-modal-btn absolute -top-3 -right-3 w-10 h-10 bg-red-500 text-white rounded-full text-2xl z-10">&times;</button>
            <h3 id="modal-title" class="text-2xl font-bold mb-4 text-center"></h3>
            <div class="swiper-container">
                <div class="swiper-wrapper"></div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-next text-red-500"></div>
                <div class="swiper-button-prev text-red-500"></div>
            </div>
        </div>
    </div>
    
    <div id="video-modal" class="modal fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 hidden opacity-0">
        <div class="w-11/12 md:w-3/4 lg:w-2/3 max-w-4xl relative">
            <button class="close-modal-btn absolute -top-10 right-0 w-10 h-10 text-white text-4xl">&times;</button>
            <div id="video-player" class="aspect-video"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/typed.js@2.0.12"></script>
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // --- TYPING EFFECT ---
            new Typed('#typing-effect', {
                strings: ['tăng tỷ lệ chốt đơn?', 'tăng giá trị đơn hàng?', 'khiến khách hàng nhớ đến bạn?'],
                typeSpeed: 50, backSpeed: 30, backDelay: 2000, loop: true, smartBackspace: true
            });

            // --- NEW CALCULATOR LOGIC ---
            const inputs = {
                currentRevenue: document.getElementById('currentRevenue'),
                currentAOV: document.getElementById('currentAOV'),
                adSpend: document.getElementById('adSpend'),
                conversionRateIncrease: document.getElementById('conversionRateIncrease'),
                aovIncrease: document.getElementById('aovIncrease'),
                bagCostPercentage: document.getElementById('bagCostPercentage'),
            };

            const outputs = {
                conversionRateIncreaseValue: document.getElementById('conversionRateIncreaseValue'),
                aovIncreaseValue: document.getElementById('aovIncreaseValue'),
                bagCostPercentageValue: document.getElementById('bagCostPercentageValue'),
                currentOrders: document.getElementById('currentOrders'),
                currentProfit: document.getElementById('currentProfit'),
                costPerBag: document.getElementById('costPerBag'),
                newOrders: document.getElementById('newOrders'),
                totalBagCost: document.getElementById('totalBagCost'),
                profitIncrease: document.getElementById('profitIncrease'),
            };

            function formatCurrency(num) {
                if (isNaN(num)) return '0đ';
                return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(num);
            }

            function calculate() {
                // 1. Read values from inputs
                const currentRevenue = parseFloat(inputs.currentRevenue.value) || 0;
                const currentAOV = parseFloat(inputs.currentAOV.value) || 0;
                const adSpend = parseFloat(inputs.adSpend.value) || 0;
                const conversionIncreasePercent = parseFloat(inputs.conversionRateIncrease.value) || 0;
                const aovIncreasePercent = parseFloat(inputs.aovIncrease.value) || 0;
                const bagCostPercent = parseFloat(inputs.bagCostPercentage.value) || 0;

                // 2. Update slider display values
                outputs.conversionRateIncreaseValue.textContent = `${conversionIncreasePercent}%`;
                outputs.aovIncreaseValue.textContent = `${aovIncreasePercent}%`;
                outputs.bagCostPercentageValue.textContent = `${bagCostPercent}%`;

                // 3. Perform calculations
                // Current State
                const currentOrders = currentAOV > 0 ? currentRevenue / currentAOV : 0;
                const currentProfit = currentRevenue - adSpend;

                // New State
                const newAOV = currentAOV * (1 + aovIncreasePercent / 100);
                const costPerBag = newAOV * (bagCostPercent / 100);
                const newOrders = currentOrders * (1 + conversionIncreasePercent / 100);
                const newRevenue = newOrders * newAOV;
                const totalBagCost = newOrders * costPerBag;
                const newProfit = newRevenue - adSpend - totalBagCost;

                // Result
                const profitIncrease = newProfit - currentProfit;

                // 4. Update output elements
                outputs.currentOrders.textContent = `${Math.round(currentOrders)} đơn`;
                outputs.currentProfit.textContent = formatCurrency(currentProfit);
                outputs.costPerBag.textContent = formatCurrency(costPerBag);
                outputs.newOrders.textContent = `${Math.round(newOrders)} đơn`;
                outputs.totalBagCost.textContent = `- ${formatCurrency(totalBagCost)}`;
                outputs.profitIncrease.textContent = formatCurrency(profitIncrease);
                
                // Change color based on profit increase
                const profitIncreaseEl = document.getElementById('profitIncrease');
                if (profitIncrease > 0) {
                    profitIncreaseEl.parentElement.classList.remove('bg-red-100', 'border-red-500', 'text-red-800');
                    profitIncreaseEl.parentElement.classList.add('bg-green-100', 'border-green-500', 'text-green-800');
                    profitIncreaseEl.classList.remove('text-red-600');
                    profitIncreaseEl.classList.add('text-green-600');
                } else {
                    profitIncreaseEl.parentElement.classList.remove('bg-green-100', 'border-green-500', 'text-green-800');
                    profitIncreaseEl.parentElement.classList.add('bg-red-100', 'border-red-500', 'text-red-800');
                    profitIncreaseEl.classList.remove('text-green-600');
                    profitIncreaseEl.classList.add('text-red-600');
                }
            }

            // Add event listeners
            Object.values(inputs).forEach(input => {
                input.addEventListener('input', calculate);
            });

            // Initial calculation on page load
            calculate();


            // --- GALLERY MODAL & SWIPER ---
            const galleryData = {
                'tote': { title: 'Túi Tote Đa Năng', images: ['https://placehold.co/800x600/EFE8DE/333?text=Tote+Mau+1', 'https://placehold.co/800x600/EFE8DE/333?text=Tote+Mau+2', 'https://placehold.co/800x600/EFE8DE/333?text=Tote+Mau+3', 'https://placehold.co/800x600/EFE8DE/333?text=Tote+Mau+4'] },
                'day-rut': { title: 'Túi Dây Rút Tiện Lợi', images: ['https://placehold.co/800x600/D9E3DA/333?text=Day+Rut+Mau+1', 'https://placehold.co/800x600/D9E3DA/333?text=Day+Rut+Mau+2', 'https://placehold.co/800x600/D9E3DA/333?text=Day+Rut+Mau+3'] },
                'hop': { title: 'Túi Hộp Đứng Form', images: ['https://placehold.co/800x600/F4D6D6/333?text=Hop+Mau+1', 'https://placehold.co/800x600/F4D6D6/333?text=Hop+Mau+2', 'https://placehold.co/800x600/F4D6D6/333?text=Hop+Mau+3', 'https://placehold.co/800x600/F4D6D6/333?text=Hop+Mau+4'] },
                'in-an': { title: 'Chất Lượng In Sắc Nét', images: ['https://placehold.co/800x600/D4E4F3/333?text=In+Logo+Brand+A', 'https://placehold.co/800x600/D4E4F3/333?text=In+Hinh+Anh+Phuc+Tap', 'https://placehold.co/800x600/D4E4F3/333?text=In+Nhieu+Mau'] }
            };
            
            const galleryModal = document.getElementById('gallery-modal');
            const galleryModalTitle = document.getElementById('modal-title');
            const swiperWrapper = galleryModal.querySelector('.swiper-wrapper');

            const swiper = new Swiper('.swiper-container', {
                loop: true,
                pagination: { el: '.swiper-pagination', clickable: true },
                navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' },
            });

            document.querySelectorAll('.gallery-item').forEach(item => {
                item.addEventListener('click', () => {
                    const category = item.dataset.category;
                    const data = galleryData[category];
                    galleryModalTitle.textContent = data.title;
                    swiperWrapper.innerHTML = '';
                    data.images.forEach(imgUrl => {
                        const slide = document.createElement('div');
                        slide.classList.add('swiper-slide');
                        slide.innerHTML = `<img src="${imgUrl}" class="w-full h-full object-contain rounded-md" style="max-height: 70vh;">`;
                        swiperWrapper.appendChild(slide);
                    });
                    swiper.update();
                    swiper.slideTo(1, 0);
                    openModal(galleryModal);
                });
            });

            // --- VIDEO MODAL ---
            const videoModal = document.getElementById('video-modal');
            const videoPlayer = document.getElementById('video-player');
            document.querySelectorAll('.video-placeholder').forEach(item => {
                item.addEventListener('click', () => {
                    const videoId = item.dataset.videoId;
                    videoPlayer.innerHTML = `<iframe class="w-full h-full" src="https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>`;
                    openModal(videoModal);
                });
            });

            // --- COMMON MODAL LOGIC ---
            function openModal(modal) {
                modal.classList.remove('hidden');
                setTimeout(() => modal.classList.remove('opacity-0'), 10);
            }

            function closeModal(modal) {
                modal.classList.add('opacity-0');
                setTimeout(() => modal.classList.add('hidden'), 300);
                 // Stop video when closing
                if(modal.id === 'video-modal') {
                    videoPlayer.innerHTML = '';
                }
            }

            document.querySelectorAll('.close-modal-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    closeModal(btn.closest('.modal'));
                });
            });

            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        closeModal(modal);
                    }
                });
            });
        });
    </script>

</body>
</html>
