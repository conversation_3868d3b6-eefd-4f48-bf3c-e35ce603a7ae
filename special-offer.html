<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lo<PERSON> - Ưu Đãi Đặc <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> Mẫu <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
    </style>
</head>
<body>
    <section style="
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        min-height: 100vh;
        padding: 80px 0;
        position: relative;
        overflow: hidden;
    ">
        <!-- Background decorative elements -->
        <div style="
            position: absolute;
            top: -50px;
            right: -50px;
            width: 200px;
            height: 200px;
            background: rgba(255, 140, 0, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        "></div>
        <div style="
            position: absolute;
            bottom: -30px;
            left: -30px;
            width: 150px;
            height: 150px;
            background: rgba(102, 126, 234, 0.08);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite reverse;
        "></div>

        <div class="container">
            <!-- Header Section -->
            <div style="
                text-align: center;
                margin-bottom: 60px;
                position: relative;
                z-index: 2;
            ">
                <h1 style="
                    font-size: clamp(2.5rem, 5vw, 4rem);
                    font-weight: 800;
                    color: #1a202c;
                    margin-bottom: 20px;
                    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                    line-height: 1.1;
                ">
                    Ưu Đãi <span style="
                        background: linear-gradient(45deg, #ff8c00, #ffd700);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    ">Đặc Biệt</span>
                </h1>
                <p style="
                    font-size: 1.2rem;
                    color: #4a5568;
                    max-width: 600px;
                    margin: 0 auto;
                    line-height: 1.6;
                ">
                    Dành cho khách hàng quan tâm thực sự đến chất lượng sản phẩm
                </p>
            </div>

            <!-- Special Offer Card -->
            <div style="
                display: flex;
                justify-content: center;
                margin-bottom: 60px;
            ">
                <div style="
                    background: white;
                    border-radius: 25px;
                    padding: 0;
                    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.1);
                    position: relative;
                    overflow: hidden;
                    max-width: 500px;
                    width: 100%;
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 35px 80px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 25px 60px rgba(0, 0, 0, 0.1)'">
                    
                    <!-- Orange Header -->
                    <div style="
                        background: linear-gradient(135deg, #ff8c00, #ff6b35);
                        padding: 20px;
                        text-align: center;
                        position: relative;
                        overflow: hidden;
                    ">
                        <div style="
                            position: absolute;
                            top: -20px;
                            right: -20px;
                            width: 80px;
                            height: 80px;
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 50%;
                        "></div>
                        <span style="
                            background: rgba(255, 255, 255, 0.9);
                            color: #ff8c00;
                            padding: 8px 20px;
                            border-radius: 20px;
                            font-size: 0.9rem;
                            font-weight: 700;
                            text-transform: uppercase;
                            letter-spacing: 1px;
                            position: relative;
                            z-index: 2;
                        ">
                            Ưu đãi có hạn
                        </span>
                    </div>

                    <!-- Card Content -->
                    <div style="
                        padding: 40px;
                        text-align: center;
                        position: relative;
                    ">
                        <h2 style="
                            font-size: 2.5rem;
                            font-weight: 800;
                            color: #1a202c;
                            margin-bottom: 15px;
                            line-height: 1.1;
                        ">
                            Túi Mẫu Miễn Phí
                        </h2>

                        <!-- Price Display -->
                        <div style="
                            margin-bottom: 25px;
                        ">
                            <div style="
                                font-size: 3.5rem;
                                font-weight: 800;
                                color: #ff8c00;
                                line-height: 1;
                                margin-bottom: 5px;
                            ">
                                30.000₫
                            </div>
                            <div style="
                                font-size: 1.2rem;
                                color: #a0aec0;
                                text-decoration: line-through;
                                margin-bottom: 10px;
                            ">
                                Giá gốc: 150.000₫
                            </div>
                        </div>

                                                 <!-- Description -->
                         <p style="
                             font-size: 1.1rem;
                             color: #4a5568;
                             margin-bottom: 30px;
                             line-height: 1.6;
                         ">
                             Chỉ thanh toán phí vận chuyển, nhận ngay <strong>một túi mẫu ngẫu nhiên từ Loma</strong> để trải nghiệm chất lượng thực tế
                         </p>

                        <!-- Benefits List -->
                        <ul style="
                            list-style: none;
                            padding: 0;
                            margin: 0 0 30px 0;
                            text-align: left;
                        ">
                            <li style="
                                display: flex;
                                align-items: center;
                                gap: 12px;
                                padding: 10px 0;
                                font-size: 1rem;
                                color: #2d3748;
                                font-weight: 500;
                            ">
                                <div style="
                                    background: linear-gradient(45deg, #48bb78, #38a169);
                                    color: white;
                                    width: 24px;
                                    height: 24px;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 0.8rem;
                                    flex-shrink: 0;
                                ">
                                    ✓
                                </div>
                                                                 1 túi mẫu ngẫu nhiên từ Loma (chất lượng cao)
                            </li>


                            <li style="
                                display: flex;
                                align-items: center;
                                gap: 12px;
                                padding: 10px 0;
                                font-size: 1rem;
                                color: #2d3748;
                                font-weight: 500;
                            ">
                                <div style="
                                    background: linear-gradient(45deg, #48bb78, #38a169);
                                    color: white;
                                    width: 24px;
                                    height: 24px;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 0.8rem;
                                    flex-shrink: 0;
                                ">
                                    ✓
                                </div>
                                Tư vấn miễn phí
                            </li>

                            <li style="
                                display: flex;
                                align-items: center;
                                gap: 12px;
                                padding: 10px 0;
                                font-size: 1rem;
                                color: #2d3748;
                                font-weight: 500;
                            ">
                                <div style="
                                    background: linear-gradient(45deg, #48bb78, #38a169);
                                    color: white;
                                    width: 24px;
                                    height: 24px;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 0.8rem;
                                    flex-shrink: 0;
                                ">
                                    ✓
                                </div>
                                Báo giá chi tiết cho đơn hàng lớn
                            </li>
                        </ul>

                        <!-- CTA Button -->
                        <button style="
                            background: linear-gradient(45deg, #ff8c00, #ff6b35);
                            color: white;
                            padding: 18px 40px;
                            border: none;
                            border-radius: 50px;
                            font-size: 1.2rem;
                            font-weight: 700;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            box-shadow: 0 10px 30px rgba(255, 140, 0, 0.3);
                            width: 100%;
                            position: relative;
                            overflow: hidden;
                            text-transform: uppercase;
                            letter-spacing: 1px;
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 15px 40px rgba(255, 140, 0, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(255, 140, 0, 0.3)'">
                            <span style="position: relative; z-index: 2;">
                                🎁 Đặt túi mẫu ngay
                            </span>
                            <div style="
                                position: absolute;
                                top: 0;
                                left: -100%;
                                width: 100%;
                                height: 100%;
                                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                                transition: left 0.5s;
                            "></div>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Additional Info Section -->
            <div style="
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 30px;
                margin-bottom: 60px;
            ">
                <!-- Info Card 1 -->
                <div style="
                    background: rgba(255, 255, 255, 0.8);
                    backdrop-filter: blur(20px);
                    border-radius: 20px;
                    padding: 30px;
                    text-align: center;
                    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 50px rgba(0, 0, 0, 0.12)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 40px rgba(0, 0, 0, 0.08)'">
                    <div style="
                        background: linear-gradient(45deg, #667eea, #764ba2);
                        color: white;
                        width: 60px;
                        height: 60px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 1.5rem;
                        margin: 0 auto 20px auto;
                        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
                    ">
                        🚚
                    </div>
                    <h3 style="
                        font-size: 1.3rem;
                        font-weight: 600;
                        color: #1a202c;
                        margin-bottom: 10px;
                    ">
                        Giao Hàng Nhanh
                    </h3>
                    <p style="
                        color: #4a5568;
                        line-height: 1.6;
                    ">
                        Nhận túi mẫu trong 5-7 ngày làm việc trên toàn quốc
                    </p>
                </div>

                <!-- Info Card 2 -->
                <div style="
                    background: rgba(255, 255, 255, 0.8);
                    backdrop-filter: blur(20px);
                    border-radius: 20px;
                    padding: 30px;
                    text-align: center;
                    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 50px rgba(0, 0, 0, 0.12)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 40px rgba(0, 0, 0, 0.08)'">
                    <div style="
                        background: linear-gradient(45deg, #ff8c00, #ffd700);
                        color: white;
                        width: 60px;
                        height: 60px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 1.5rem;
                        margin: 0 auto 20px auto;
                        box-shadow: 0 10px 25px rgba(255, 140, 0, 0.3);
                    ">
                        💼
                    </div>
                    <h3 style="
                        font-size: 1.3rem;
                        font-weight: 600;
                        color: #1a202c;
                        margin-bottom: 10px;
                    ">
                        Tư Vấn Chuyên Nghiệp
                    </h3>
                    <p style="
                        color: #4a5568;
                        line-height: 1.6;
                    ">
                        Đội ngũ tư vấn 5+ năm kinh nghiệm hỗ trợ 24/7
                    </p>
                </div>

                <!-- Info Card 3 -->
                <div style="
                    background: rgba(255, 255, 255, 0.8);
                    backdrop-filter: blur(20px);
                    border-radius: 20px;
                    padding: 30px;
                    text-align: center;
                    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 50px rgba(0, 0, 0, 0.12)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 15px 40px rgba(0, 0, 0, 0.08)'">
                    <div style="
                        background: linear-gradient(45deg, #48bb78, #38a169);
                        color: white;
                        width: 60px;
                        height: 60px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 1.5rem;
                        margin: 0 auto 20px auto;
                        box-shadow: 0 10px 25px rgba(72, 187, 120, 0.3);
                    ">
                        ⭐
                    </div>
                    <h3 style="
                        font-size: 1.3rem;
                        font-weight: 600;
                        color: #1a202c;
                        margin-bottom: 10px;
                    ">
                        Chất Lượng Đảm Bảo
                    </h3>
                    <p style="
                        color: #4a5568;
                        line-height: 1.6;
                    ">
                        Kiểm soát chất lượng nghiêm ngặt từng sản phẩm
                    </p>
                </div>
            </div>

            <!-- Bottom CTA Section -->
            <div style="
                text-align: center;
                background: rgba(255, 255, 255, 0.6);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 40px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                position: relative;
                overflow: hidden;
            ">
                <div style="
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 300px;
                    height: 300px;
                    background: radial-gradient(circle, rgba(255, 140, 0, 0.1) 0%, transparent 70%);
                    border-radius: 50%;
                "></div>

                <h2 style="
                    font-size: 2.2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin-bottom: 15px;
                    position: relative;
                    z-index: 2;
                ">
                    Còn Chần Chừ Gì Nữa?
                </h2>
                <p style="
                    font-size: 1.1rem;
                    color: #4a5568;
                    margin-bottom: 30px;
                    max-width: 500px;
                    margin-left: auto;
                    margin-right: auto;
                    position: relative;
                    z-index: 2;
                ">
                                         Chỉ với 30.000₫ phí vận chuyển, bạn sẽ có ngay một túi mẫu ngẫu nhiên từ Loma để trải nghiệm chất lượng thực tế
                </p>
                
                <div style="
                    display: flex;
                    gap: 20px;
                    justify-content: center;
                    flex-wrap: wrap;
                    position: relative;
                    z-index: 2;
                ">
                    <button style="
                        background: linear-gradient(45deg, #ff8c00, #ff6b35);
                        color: white;
                        padding: 15px 30px;
                        border: none;
                        border-radius: 50px;
                        font-size: 1.1rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        box-shadow: 0 8px 25px rgba(255, 140, 0, 0.3);
                        position: relative;
                        overflow: hidden;
                        min-width: 220px;
                    " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 12px 35px rgba(255, 140, 0, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 25px rgba(255, 140, 0, 0.3)'">
                        🛍️ Đặt Mẫu Ngay
                    </button>
                    
                    <button style="
                        background: linear-gradient(45deg, #667eea, #764ba2);
                        color: white;
                        padding: 15px 30px;
                        border: none;
                        border-radius: 50px;
                        font-size: 1.1rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
                        position: relative;
                        overflow: hidden;
                        min-width: 200px;
                    " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 12px 35px rgba(102, 126, 234, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 25px rgba(102, 126, 234, 0.3)'">
                                                 💰 Yêu Cầu Báo Giá
                    </button>
                </div>
            </div>
        </div>
    </section>

    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            section {
                padding: 60px 0 !important;
            }

            [style*="padding: 40px"] {
                padding: 25px !important;
            }

            [style*="padding: 30px"] {
                padding: 20px !important;
            }

            [style*="font-size: clamp(2.5rem, 5vw, 4rem)"] {
                font-size: 2.2rem !important;
            }

            [style*="font-size: 2.5rem"] {
                font-size: 2rem !important;
            }

            [style*="font-size: 3.5rem"] {
                font-size: 2.8rem !important;
            }

            [style*="font-size: 2.2rem"] {
                font-size: 1.8rem !important;
            }

            [style*="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap"] {
                flex-direction: column !important;
                align-items: center !important;
            }

            [style*="min-width: 200px"], [style*="min-width: 220px"] {
                width: 100% !important;
                max-width: 280px !important;
            }

            [style*="grid-template-columns: repeat(auto-fit, minmax(300px, 1fr))"] {
                grid-template-columns: 1fr !important;
            }
        }

        @media (max-width: 480px) {
            [style*="padding: 25px"] {
                padding: 20px !important;
            }

            [style*="padding: 20px"] {
                padding: 15px !important;
            }

            [style*="font-size: 2.2rem"] {
                font-size: 1.8rem !important;
            }

            [style*="font-size: 2rem"] {
                font-size: 1.6rem !important;
            }

            [style*="font-size: 2.8rem"] {
                font-size: 2.2rem !important;
            }

            [style*="font-size: 1.8rem"] {
                font-size: 1.5rem !important;
            }

            [style*="gap: 30px"] {
                gap: 20px !important;
            }

            [style*="font-size: 1.1rem"] {
                font-size: 1rem !important;
            }

            [style*="padding: 18px 40px"] {
                padding: 15px 30px !important;
            }
        }
    </style>
</body>
</html>