<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Lo<PERSON> - <PERSON><PERSON> In Logo <PERSON></title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="assets/css/common.css" />

    <style>
      :root {
        --primary-color: #4299e1;
        --secondary-color: #ff9500;
        --accent-color: #48bb78;
        --orange-light: #ffb347;
      }

      .hero-loma {
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        position: relative;
      }

      .hero-loma::before {
        content: "";
        position: absolute;
        top: 20%;
        right: 10%;
        width: 200px;
        height: 200px;
        background: linear-gradient(
          135deg,
          rgba(255, 149, 0, 0.1) 0%,
          rgba(255, 179, 71, 0.1) 100%
        );
        border-radius: 50%;
        z-index: 1;
      }

      .hero-loma::after {
        content: "";
        position: absolute;
        bottom: 10%;
        left: 5%;
        width: 150px;
        height: 150px;
        background: linear-gradient(
          135deg,
          rgba(66, 153, 225, 0.1) 0%,
          rgba(102, 126, 234, 0.1) 100%
        );
        border-radius: 50%;
        z-index: 1;
      }

      .hero-content {
        position: relative;
        z-index: 2;
      }

      .offer-highlight {
        background: linear-gradient(135deg, #ff9500 0%, #ffb347 100%);
        color: white;
        padding: 80px 0;
        position: relative;
        overflow: hidden;
      }

      .offer-highlight::before {
        content: "";
        position: absolute;
        top: -50%;
        right: -20%;
        width: 300px;
        height: 300px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        z-index: 1;
      }

      .modern-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 149, 0, 0.1);
      }

      .modern-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
        border-color: rgba(255, 149, 0, 0.2);
      }

      .orange-accent {
        color: var(--secondary-color);
        font-weight: 600;
      }

      .stats-bg-modern {
        background: linear-gradient(135deg, #4299e1 0%, #667eea 100%);
        color: white;
        position: relative;
        overflow: hidden;
      }

      .stats-bg-modern::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      }

      .feature-icon-orange {
        background: linear-gradient(135deg, #ff9500 0%, #ffb347 100%);
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
        margin: 0 auto 20px;
        box-shadow: 0 10px 30px rgba(255, 149, 0, 0.3);
      }

              .feature-icon-blue {
            background: linear-gradient(135deg, #4299e1 0%, #667eea 100%);
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(66, 153, 225, 0.3);
        }
        
        /* Mobile Menu Button */
        .mobile-menu-btn {
            display: none;
            flex-direction: column;
            gap: 4px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .mobile-menu-btn span {
            width: 25px;
            height: 3px;
            background: #333;
            border-radius: 3px;
            transition: all 0.3s ease;
        }
        
        .mobile-menu-btn:hover {
            background: rgba(0,0,0,0.05);
        }
        
        .mobile-menu-btn.active span:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }
        
        .mobile-menu-btn.active span:nth-child(2) {
            opacity: 0;
        }
        
        .mobile-menu-btn.active span:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

      /* Animation keyframes */
      @keyframes floatUp {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-15px);
        }
      }

      @keyframes floatDown {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(15px);
        }
      }

      @keyframes blobMove {
        0%,
        100% {
          border-radius: 45% 55% 62% 38% / 45% 65% 35% 55%;
        }
        25% {
          border-radius: 62% 38% 45% 55% / 65% 35% 55% 45%;
        }
        50% {
          border-radius: 38% 62% 55% 45% / 35% 55% 45% 65%;
        }
        75% {
          border-radius: 55% 45% 38% 62% / 55% 45% 65% 35%;
        }
      }

      .floating-icon {
        transition: all 0.3s ease;
      }

      .floating-icon:hover {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2) !important;
      }

      /* Mobile responsive fixes */
      @media (max-width: 768px) {
        body {
          overflow-x: hidden;
        }

        .container {
          max-width: 100% !important;
          padding: 0 15px !important;
        }

        .hero-loma {
          padding: 60px 0 40px !important;
          overflow-x: hidden;
          min-height: auto !important;
        }

        .hero-loma .row {
          min-height: auto !important;
          flex-direction: column;
          margin: 0 !important;
        }

        .hero-loma .col {
          width: 100% !important;
          padding: 20px 0 !important;
          flex: none !important;
        }

        .hero-content {
          text-align: center !important;
          order: 2;
          margin-top: 30px !important;
        }

        .hero-content h1 {
          font-size: 2rem !important;
          line-height: 1.1 !important;
          margin-bottom: 15px !important;
        }

        .hero-content p {
          font-size: 0.95rem !important;
          margin-bottom: 20px !important;
          line-height: 1.5 !important;
        }

        .btn-mobile-stack {
          flex-direction: column !important;
          gap: 12px !important;
          align-items: stretch !important;
          max-width: 280px !important;
          margin: 0 auto !important;
        }

        .btn-mobile-stack .btn {
          width: 100% !important;
          text-align: center !important;
          justify-content: center !important;
          padding: 14px 20px !important;
          font-size: 0.9rem !important;
        }

        .hero-image-mobile {
          max-width: 250px !important;
          margin: 0 auto !important;
          padding: 0 !important;
          order: 1;
        }

        .hero-image-mobile > div {
          width: 250px !important;
          height: 250px !important;
          margin: 0 auto !important;
        }

        .hero-image-mobile img {
          width: 200px !important;
          height: 200px !important;
        }

        /* Hide all floating elements on mobile for cleaner look */
        .floating-icon,
        .hero-image-mobile .floating-icon,
        .hero-image-mobile > div > div:not(img),
        .hero-loma::before,
        .hero-loma::after {
          display: none !important;
        }

        /* Mobile stats styling */
        .stats-mobile {
          margin-top: 25px !important;
          padding-top: 25px !important;
          border-top: 1px solid #e2e8f0 !important;
        }

        .stats-mobile .row {
          justify-content: center !important;
          text-align: center !important;
        }

        .stats-mobile .col {
          margin-bottom: 15px !important;
          flex: 0 0 33.333% !important;
          padding: 0 10px !important;
        }

        .stats-mobile .col > div {
          padding: 12px 8px !important;
          background: rgba(255, 255, 255, 0.9) !important;
          border-radius: 12px !important;
          box-shadow: 0 2px 10px rgba(0,0,0,0.05) !important;
        }

        .stats-mobile .col > div > div:first-child {
          font-size: 1.5rem !important;
          font-weight: 700 !important;
          margin-bottom: 3px !important;
        }

        .stats-mobile .col > div > div:last-child {
          font-size: 0.75rem !important;
          line-height: 1.2 !important;
        }

        /* Fix hero content text sizing */
        .hero-content .orange-accent {
          font-size: 0.75rem !important;
          padding: 6px 12px !important;
          margin-bottom: 15px !important;
        }

        .d-flex.align-center.gap-2 {
          justify-content: center !important;
          margin-bottom: 15px !important;
        }

        .d-flex.align-center.gap-2 span {
          font-size: 0.85rem !important;
        }

        /* Fix other sections mobile */
        .section {
          padding: 50px 0 !important;
        }

        .modern-card {
          margin-bottom: 20px !important;
          padding: 20px !important;
        }

        .row .col {
          flex: 0 0 100% !important;
          margin-bottom: 20px !important;
        }

        /* Fix navigation on mobile */
        .nav-menu {
          display: none !important;
        }
        
        .mobile-menu-btn {
          display: flex !important;
        }

        .logo {
          font-size: 1.5rem !important;
        }

        /* Fix offer section mobile */
        .offer-highlight {
          padding: 50px 0 !important;
        }

        .offer-highlight .modern-card,
        .offer-highlight > div > div {
          padding: 25px 15px !important;
          margin: 0 10px !important;
        }
        
        /* Fix footer mobile */
        footer .row .col {
          flex: 0 0 100% !important;
          text-align: center !important;
          margin-bottom: 30px !important;
        }
        
        footer .row .col:last-child {
          margin-bottom: 0 !important;
        }
        
        /* Fix form mobile */
        .modern-card .row .col {
          flex: 0 0 100% !important;
        }
        
        .form-input,
        .form-select,
        .form-textarea {
          width: 100% !important;
          margin-bottom: 15px !important;
        }
      }

      /* Additional small screen fixes */
      @media (max-width: 480px) {
        .container {
          padding: 0 10px !important;
        }

        .hero-content h1 {
          font-size: 1.8rem !important;
        }

        .hero-image-mobile {
          max-width: 220px !important;
        }

        .hero-image-mobile > div {
          width: 220px !important;
          height: 220px !important;
        }

        .hero-image-mobile img {
          width: 180px !important;
          height: 180px !important;
        }

        .btn-mobile-stack {
          max-width: 250px !important;
        }

        .stats-mobile .col {
          flex: 0 0 100% !important;
          margin-bottom: 10px !important;
        }

        .stats-mobile .col > div {
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          gap: 15px !important;
          text-align: left !important;
        }

        .stats-mobile .col > div > div:last-child {
          line-height: 1.3 !important;
        }
        
        /* Fix footer on very small screens */
        footer .row .col {
          padding: 15px !important;
        }
        
        footer .row .col h3,
        footer .row .col h4,
        footer .row .col h5 {
          font-size: 1.2rem !important;
        }
        
        /* Fix offer section on small screens */
        .offer-highlight h2 {
          font-size: 1.8rem !important;
        }
        
        .offer-highlight h3 {
          font-size: 1.5rem !important;
        }
        
        .offer-highlight h4 {
          font-size: 1.3rem !important;
        }
      }
    </style>
  </head>
  <body>
    <header class="header">
      <div class="container">
        <nav class="nav">
          <a href="#" class="logo">
            <i class="fas fa-shopping-bag"></i> Loma Bag
          </a>
                          <ul class="nav-menu">
                    <li><a href="#gioi-thieu" class="nav-link">Giới thiệu</a></li>
                    <li><a href="#san-pham" class="nav-link">Sản phẩm</a></li>
                    <li><a href="#bao-gia" class="nav-link">Báo giá</a></li>
                    <li><a href="#lien-he" class="nav-link">Liên hệ</a></li>
                </ul>
                <button class="mobile-menu-btn">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
        </nav>
      </div>
    </header>

    <section class="hero hero-loma">
      <div class="container">
        <div class="row align-center" style="min-height: 90vh">
          <div class="col col-6">
            <div class="hero-content scroll-reveal">
              <div
                style="
                  background: linear-gradient(135deg, #ff9500, #ffb347);
                  color: white;
                  padding: 8px 16px;
                  border-radius: 20px;
                  display: inline-block;
                  margin-bottom: 20px;
                  font-size: 0.9rem;
                  font-weight: 600;
                "
              >
                ✨ Xưởng sản xuất túi vải #1 Việt Nam
              </div>
              <h1
                style="
                  font-size: 3.5rem;
                  line-height: 1.2;
                  margin-bottom: 20px;
                  color: #1a1a1a;
                "
              >
                Sản Phẩm Tốt Hơn Cho
                <span style="color: #ff9500; display: block"
                  >Khách Hàng Của Bạn</span
                >
              </h1>
              <p
                style="
                  font-size: 1.2rem;
                  line-height: 1.6;
                  color: #4a5568;
                  margin-bottom: 30px;
                "
              >
                Xưởng sản xuất túi vải in logo hàng đầu cho các shop online và
                thương hiệu. Chất lượng cao, giá cả cạnh tranh, giao hàng nhanh
                chóng.
              </p>

              <div class="d-flex align-center gap-4 mb-4">
                <div class="d-flex align-center gap-2">
                  <div
                    style="
                      width: 12px;
                      height: 12px;
                      background: #48bb78;
                      border-radius: 50%;
                    "
                  ></div>
                  <span
                    style="font-size: 1rem; font-weight: 500; color: #2d3748"
                    >Giao hàng trong 3-5 ngày</span
                  >
                </div>
              </div>

              <div class="d-flex gap-3 mt-4 btn-mobile-stack">
                <a
                  href="#bao-gia"
                  class="btn"
                  style="
                    background: linear-gradient(135deg, #4299e1, #667eea);
                    color: white;
                    padding: 18px 35px;
                    border-radius: 50px;
                    font-weight: 600;
                    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.3);
                    border: none;
                    font-size: 1.1rem;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  <i
                    class="fas fa-file-invoice-dollar"
                    style="margin-right: 8px"
                  ></i>
                  Nhận Báo Giá
                </a>
                <a
                  href="#offer"
                  class="btn"
                  style="
                    background: linear-gradient(135deg, #ff9500, #ffb347);
                    color: white;
                    padding: 18px 35px;
                    border-radius: 50px;
                    font-weight: 600;
                    box-shadow: 0 8px 25px rgba(255, 149, 0, 0.3);
                    border: none;
                    font-size: 1.1rem;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  <i class="fas fa-gift" style="margin-right: 8px"></i>
                  Xem Ưu Đãi
                </a>
              </div>

              <!-- Stats mini -->
              <div
                class="row mt-5 stats-mobile"
                style="border-top: 1px solid #e2e8f0; padding-top: 30px"
              >
                <div class="col col-4">
                  <div style="text-align: center">
                    <div
                      style="
                        font-size: 2rem;
                        font-weight: 700;
                        color: #1a1a1a;
                        margin-bottom: 5px;
                      "
                    >
                      500+
                    </div>
                    <div style="font-size: 0.9rem; color: #718096">
                      Thương hiệu<br />Tin tưởng
                    </div>
                  </div>
                </div>
                <div class="col col-4">
                  <div style="text-align: center">
                    <div
                      style="
                        font-size: 2rem;
                        font-weight: 700;
                        color: #1a1a1a;
                        margin-bottom: 5px;
                      "
                    >
                      50k+
                    </div>
                    <div style="font-size: 0.9rem; color: #718096">
                      Túi vải<br />Đã sản xuất
                    </div>
                  </div>
                </div>
                <div class="col col-4">
                  <div style="text-align: center">
                    <div
                      style="
                        font-size: 2rem;
                        font-weight: 700;
                        color: #1a1a1a;
                        margin-bottom: 5px;
                      "
                    >
                      98%
                    </div>
                    <div style="font-size: 0.9rem; color: #718096">
                      Khách hàng<br />Hài lòng
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col col-6">
            <div
              class="scroll-reveal hero-image-mobile"
              style="position: relative; text-align: center"
            >
              <!-- Main orange blob background -->
              <div style="position: relative; display: inline-block">
                <div
                  style="
                    width: 450px;
                    height: 450px;
                    background: linear-gradient(135deg, #ff6b35, #ff9500);
                    border-radius: 45% 55% 62% 38% / 45% 65% 35% 55%;
                    position: relative;
                    margin: 0 auto;
                    animation: blobMove 6s ease-in-out infinite;
                  "
                >
                  <img
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
                    alt="Chuyên gia túi vải"
                    style="
                      width: 380px;
                      height: 380px;
                      border-radius: 50%;
                      object-fit: cover;
                      position: absolute;
                      top: 50%;
                      left: 50%;
                      transform: translate(-50%, -50%);
                      border: 8px solid white;
                      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
                    "
                  />
                </div>
              </div>

              <!-- Floating icon elements with animation -->
              <div
                class="floating-icon"
                style="
                  position: absolute;
                  top: 80px;
                  left: 60px;
                  background: white;
                  padding: 15px;
                  border-radius: 20px;
                  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                  z-index: 10;
                  animation: floatUp 3s ease-in-out infinite;
                "
              >
                <div
                  style="
                    width: 45px;
                    height: 45px;
                    background: linear-gradient(135deg, #4299e1, #667eea);
                    border-radius: 15px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  <i
                    class="fas fa-shopping-bag"
                    style="color: white; font-size: 1.3rem"
                  ></i>
                </div>
              </div>

              <div
                class="floating-icon"
                style="
                  position: absolute;
                  top: 200px;
                  right: 30px;
                  background: white;
                  padding: 15px;
                  border-radius: 20px;
                  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                  z-index: 10;
                  animation: floatDown 4s ease-in-out infinite;
                "
              >
                <div
                  style="
                    width: 45px;
                    height: 45px;
                    background: linear-gradient(135deg, #ff9500, #ffb347);
                    border-radius: 15px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  <i
                    class="fas fa-sparkles"
                    style="color: white; font-size: 1.3rem"
                  ></i>
                </div>
              </div>

              <div
                class="floating-icon"
                style="
                  position: absolute;
                  bottom: 150px;
                  left: 30px;
                  background: white;
                  padding: 15px;
                  border-radius: 20px;
                  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                  z-index: 10;
                  animation: floatUp 3.5s ease-in-out infinite;
                "
              >
                <div
                  style="
                    width: 45px;
                    height: 45px;
                    background: linear-gradient(135deg, #e53e3e, #fc8181);
                    border-radius: 15px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  <i
                    class="fas fa-heart"
                    style="color: white; font-size: 1.3rem"
                  ></i>
                </div>
              </div>

              <!-- Stats cards with animation -->
              <div
                style="
                  position: absolute;
                  top: 30px;
                  right: 20px;
                  background: white;
                  padding: 12px 18px;
                  border-radius: 20px;
                  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                  z-index: 10;
                  animation: floatDown 4.5s ease-in-out infinite;
                "
              >
                <div style="display: flex; align-items: center; gap: 10px">
                  <div
                    style="
                      width: 40px;
                      height: 40px;
                      background: linear-gradient(135deg, #48bb78, #38a169);
                      border-radius: 12px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                    "
                  >
                    <i
                      class="fas fa-building"
                      style="color: white; font-size: 1rem"
                    ></i>
                  </div>
                  <div>
                    <div
                      style="
                        font-weight: 700;
                        color: #1a1a1a;
                        font-size: 1.2rem;
                      "
                    >
                      500+
                    </div>
                    <div style="color: #718096; font-size: 0.8rem">Brands</div>
                  </div>
                </div>
              </div>

              <div
                style="
                  position: absolute;
                  bottom: 80px;
                  right: 50px;
                  background: white;
                  padding: 12px 18px;
                  border-radius: 20px;
                  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                  z-index: 10;
                  animation: floatUp 3.8s ease-in-out infinite;
                "
              >
                <div style="display: flex; align-items: center; gap: 10px">
                  <div
                    style="
                      width: 40px;
                      height: 40px;
                      background: linear-gradient(135deg, #9f7aea, #805ad5);
                      border-radius: 12px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                    "
                  >
                    <i
                      class="fas fa-star"
                      style="color: white; font-size: 1rem"
                    ></i>
                  </div>
                  <div>
                    <div
                      style="
                        font-weight: 700;
                        color: #1a1a1a;
                        font-size: 1.2rem;
                      "
                    >
                      98%
                    </div>
                    <div style="color: #718096; font-size: 0.8rem">
                      Satisfied
                    </div>
                  </div>
                </div>
              </div>

              <!-- Small floating elements -->
              <div
                style="
                  position: absolute;
                  top: 120px;
                  left: 150px;
                  width: 25px;
                  height: 25px;
                  background: linear-gradient(135deg, #ffd93d, #ff6b35);
                  border-radius: 8px;
                  animation: floatDown 2.5s ease-in-out infinite;
                  opacity: 0.8;
                "
              ></div>
              <div
                style="
                  position: absolute;
                  top: 280px;
                  left: 80px;
                  width: 20px;
                  height: 20px;
                  background: linear-gradient(135deg, #74b9ff, #0984e3);
                  border-radius: 50%;
                  animation: floatUp 3.2s ease-in-out infinite;
                  opacity: 0.7;
                "
              ></div>
              <div
                style="
                  position: absolute;
                  bottom: 200px;
                  right: 120px;
                  width: 18px;
                  height: 18px;
                  background: linear-gradient(135deg, #fd79a8, #e84393);
                  border-radius: 5px;
                  animation: floatDown 2.8s ease-in-out infinite;
                  opacity: 0.6;
                "
              ></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="section stats-bg-modern">
      <div class="container">
        <div class="row text-center">
          <div class="col col-4">
            <div class="scroll-reveal">
              <span
                class="stats-number"
                data-target="500"
                style="color: white; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1)"
                >0</span
              >
              <p class="mb-0" style="font-weight: 500; opacity: 0.9">
                Thương Hiệu Tin Tưởng
              </p>
            </div>
          </div>
          <div class="col col-4">
            <div class="scroll-reveal">
              <span
                class="stats-number"
                data-target="50000"
                style="color: white; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1)"
                >0</span
              >
              <p class="mb-0" style="font-weight: 500; opacity: 0.9">
                Túi Vải Đã Sản Xuất
              </p>
            </div>
          </div>
          <div class="col col-4">
            <div class="scroll-reveal">
              <span
                class="stats-number"
                data-target="98"
                style="color: white; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1)"
                >0</span
              >
              <p class="mb-0" style="font-weight: 500; opacity: 0.9">
                % Khách Hàng Hài Lòng
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="gioi-thieu" class="section" style="padding: 100px 0">
      <div class="container">
        <div class="row align-center">
          <div class="col col-6">
            <div class="scroll-reveal" style="position: relative">
              <!-- Main illustration -->
              <div
                style="
                  position: relative;
                  background: linear-gradient(
                    135deg,
                    #f0f9ff 0%,
                    #e0f2fe 50%,
                    #bae6fd 100%
                  );
                  border-radius: 30px;
                  padding: 40px;
                  overflow: hidden;
                "
              >
                <!-- Background elements -->
                <div
                  style="
                    position: absolute;
                    top: -20px;
                    right: -20px;
                    width: 120px;
                    height: 120px;
                    background: linear-gradient(
                      135deg,
                      rgba(255, 149, 0, 0.1),
                      rgba(255, 179, 71, 0.1)
                    );
                    border-radius: 50%;
                  "
                ></div>
                <div
                  style="
                    position: absolute;
                    bottom: -30px;
                    left: -30px;
                    width: 100px;
                    height: 100px;
                    background: linear-gradient(
                      135deg,
                      rgba(66, 153, 225, 0.1),
                      rgba(102, 126, 234, 0.1)
                    );
                    border-radius: 50%;
                  "
                ></div>

                <!-- Team working illustration -->
                <div style="text-align: center; position: relative; z-index: 2">
                  <div style="display: inline-block; position: relative">
                    <img
                      src="https://images.unsplash.com/photo-1531545514256-b1400bc00f31?w=350&h=280&fit=crop"
                      alt="Team working"
                      style="
                        width: 350px;
                        height: 280px;
                        border-radius: 20px;
                        object-fit: cover;
                        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                      "
                    />

                    <!-- Floating elements around image -->
                    <div
                      style="
                        position: absolute;
                        top: -15px;
                        left: -15px;
                        background: white;
                        padding: 12px;
                        border-radius: 12px;
                        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                      "
                    >
                      <div
                        style="
                          width: 40px;
                          height: 40px;
                          background: linear-gradient(135deg, #48bb78, #38a169);
                          border-radius: 10px;
                          display: flex;
                          align-items: center;
                          justify-content: center;
                        "
                      >
                        <i
                          class="fas fa-leaf"
                          style="color: white; font-size: 1rem"
                        ></i>
                      </div>
                    </div>

                    <div
                      style="
                        position: absolute;
                        top: -10px;
                        right: -20px;
                        background: white;
                        padding: 10px 15px;
                        border-radius: 20px;
                        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                        font-size: 0.8rem;
                        font-weight: 600;
                        color: #1a1a1a;
                      "
                    >
                      🎯 /
                    </div>

                    <div
                      style="
                        position: absolute;
                        bottom: -15px;
                        right: -15px;
                        background: white;
                        padding: 12px;
                        border-radius: 12px;
                        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                      "
                    >
                      <div
                        style="
                          width: 40px;
                          height: 40px;
                          background: linear-gradient(135deg, #ff9500, #ffb347);
                          border-radius: 10px;
                          display: flex;
                          align-items: center;
                          justify-content: center;
                        "
                      >
                        <i
                          class="fas fa-laptop"
                          style="color: white; font-size: 1rem"
                        ></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col col-6">
            <div class="scroll-reveal" style="padding-left: 50px">
              <h2
                style="
                  font-size: 3rem;
                  line-height: 1.2;
                  margin-bottom: 25px;
                  color: #1a1a1a;
                  font-weight: 700;
                "
              >
                Cách Chúng Tôi Phục Vụ
              </h2>
              <p
                style="
                  font-size: 1.1rem;
                  line-height: 1.7;
                  color: #4a5568;
                  margin-bottom: 40px;
                "
              >
                Với hơn 5 năm kinh nghiệm trong ngành sản xuất túi vải, Loma Bag
                đã trở thành đối tác tin cậy của hàng trăm shop online và thương
                hiệu với quy trình làm việc chuyên nghiệp.
              </p>

              <!-- Process steps -->
              <div style="margin-bottom: 30px">
                <div
                  style="
                    display: flex;
                    align-items: flex-start;
                    gap: 15px;
                    margin-bottom: 25px;
                  "
                >
                  <div
                    style="
                      width: 50px;
                      height: 50px;
                      background: linear-gradient(135deg, #4299e1, #667eea);
                      border-radius: 12px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      flex-shrink: 0;
                    "
                  >
                    <span
                      style="color: white; font-weight: 700; font-size: 1.1rem"
                      >1</span
                    >
                  </div>
                  <div>
                    <h4
                      style="
                        margin: 0 0 8px 0;
                        color: #1a1a1a;
                        font-weight: 600;
                        font-size: 1.2rem;
                      "
                    >
                      Tư Vấn Thiết Kế
                    </h4>
                    <p style="margin: 0; color: #4a5568; line-height: 1.5">
                      Đội ngũ thiết kế chuyên nghiệp tư vấn và tạo ra mẫu thiết
                      kế phù hợp với thương hiệu của bạn
                    </p>
                  </div>
                </div>

                <div
                  style="
                    display: flex;
                    align-items: flex-start;
                    gap: 15px;
                    margin-bottom: 25px;
                  "
                >
                  <div
                    style="
                      width: 50px;
                      height: 50px;
                      background: linear-gradient(135deg, #ff9500, #ffb347);
                      border-radius: 12px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      flex-shrink: 0;
                    "
                  >
                    <span
                      style="color: white; font-weight: 700; font-size: 1.1rem"
                      >2</span
                    >
                  </div>
                  <div>
                    <h4
                      style="
                        margin: 0 0 8px 0;
                        color: #1a1a1a;
                        font-weight: 600;
                        font-size: 1.2rem;
                      "
                    >
                      Sản Xuất Chất Lượng
                    </h4>
                    <p style="margin: 0; color: #4a5568; line-height: 1.5">
                      Sử dụng máy móc hiện đại và nguyên liệu cao cấp để đảm bảo
                      chất lượng sản phẩm tốt nhất
                    </p>
                  </div>
                </div>

                <div style="display: flex; align-items: flex-start; gap: 15px">
                  <div
                    style="
                      width: 50px;
                      height: 50px;
                      background: linear-gradient(135deg, #48bb78, #38a169);
                      border-radius: 12px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      flex-shrink: 0;
                    "
                  >
                    <span
                      style="color: white; font-weight: 700; font-size: 1.1rem"
                      >3</span
                    >
                  </div>
                  <div>
                    <h4
                      style="
                        margin: 0 0 8px 0;
                        color: #1a1a1a;
                        font-weight: 600;
                        font-size: 1.2rem;
                      "
                    >
                      Giao Hàng Nhanh Chóng
                    </h4>
                    <p style="margin: 0; color: #4a5568; line-height: 1.5">
                      Hệ thống logistics hoàn thiện, đảm bảo giao hàng đúng hẹn
                      trong vòng 3-5 ngày
                    </p>
                  </div>
                </div>
              </div>

              <a
                href="#bao-gia"
                class="btn"
                style="
                  background: linear-gradient(135deg, #4299e1, #667eea);
                  color: white;
                  padding: 15px 35px;
                  border-radius: 50px;
                  font-weight: 600;
                  box-shadow: 0 8px 25px rgba(66, 153, 225, 0.3);
                  border: none;
                  font-size: 1.1rem;
                  margin-top: 20px;
                "
              >
                Tìm Hiểu Thêm
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="san-pham" class="section" style="background: #f8f9fa">
      <div class="container">
        <div class="text-center mb-5">
          <h2 class="scroll-reveal">Sản Phẩm Của Chúng Tôi</h2>
          <p class="scroll-reveal">
            Đa dạng mẫu mã, kích thước để phù hợp với mọi nhu cầu
          </p>
        </div>

        <div class="row">
          <div class="col col-3">
            <div class="modern-card text-center scroll-reveal">
              <img
                src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=200&fit=crop"
                alt="Túi tote canvas"
                style="
                  width: 100%;
                  height: 200px;
                  object-fit: cover;
                  border-radius: 15px;
                  margin-bottom: 20px;
                "
              />
              <h4 class="mt-3" style="color: #1a1a1a; font-weight: 600">
                Túi Tote Canvas
              </h4>
              <p style="color: #4a5568; font-size: 0.95rem">
                Kích thước: <span class="orange-accent">35x40cm</span><br />Chất
                liệu: Canvas dày dặn
              </p>
              <div
                style="
                  background: linear-gradient(135deg, #ff9500, #ffb347);
                  color: white;
                  padding: 8px 20px;
                  border-radius: 25px;
                  display: inline-block;
                  font-weight: 600;
                  box-shadow: 0 4px 15px rgba(255, 149, 0, 0.3);
                "
              >
                Từ 25,000đ/chiếc
              </div>
            </div>
          </div>

          <div class="col col-3">
            <div class="modern-card text-center scroll-reveal">
              <img
                src="https://images.unsplash.com/photo-1547949003-9792a18a2601?w=300&h=200&fit=crop"
                alt="Túi cotton"
                style="
                  width: 100%;
                  height: 200px;
                  object-fit: cover;
                  border-radius: 15px;
                  margin-bottom: 20px;
                "
              />
              <h4 class="mt-3" style="color: #1a1a1a; font-weight: 600">
                Túi Cotton Eco
              </h4>
              <p style="color: #4a5568; font-size: 0.95rem">
                Kích thước: <span class="orange-accent">30x35cm</span><br />Chất
                liệu: Cotton 100%
              </p>
              <div
                style="
                  background: linear-gradient(135deg, #ff9500, #ffb347);
                  color: white;
                  padding: 8px 20px;
                  border-radius: 25px;
                  display: inline-block;
                  font-weight: 600;
                  box-shadow: 0 4px 15px rgba(255, 149, 0, 0.3);
                "
              >
                Từ 22,000đ/chiếc
              </div>
            </div>
          </div>

          <div class="col col-3">
            <div class="modern-card text-center scroll-reveal">
              <img
                src="https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=300&h=200&fit=crop"
                alt="Túi có quai"
                style="
                  width: 100%;
                  height: 200px;
                  object-fit: cover;
                  border-radius: 15px;
                  margin-bottom: 20px;
                "
              />
              <h4 class="mt-3" style="color: #1a1a1a; font-weight: 600">
                Túi Vải Có Quai
              </h4>
              <p style="color: #4a5568; font-size: 0.95rem">
                Kích thước: <span class="orange-accent">25x30cm</span><br />Quai
                dài tiện lợi
              </p>
              <div
                style="
                  background: linear-gradient(135deg, #ff9500, #ffb347);
                  color: white;
                  padding: 8px 20px;
                  border-radius: 25px;
                  display: inline-block;
                  font-weight: 600;
                  box-shadow: 0 4px 15px rgba(255, 149, 0, 0.3);
                "
              >
                Từ 20,000đ/chiếc
              </div>
            </div>
          </div>

          <div class="col col-3">
            <div class="modern-card text-center scroll-reveal">
              <img
                src="https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300&h=200&fit=crop"
                alt="Túi premium"
                style="
                  width: 100%;
                  height: 200px;
                  object-fit: cover;
                  border-radius: 15px;
                  margin-bottom: 20px;
                "
              />
              <h4 class="mt-3" style="color: #1a1a1a; font-weight: 600">
                Túi Premium
              </h4>
              <p style="color: #4a5568; font-size: 0.95rem">
                Kích thước: <span class="orange-accent">40x45cm</span><br />Chất
                liệu cao cấp
              </p>
              <div
                style="
                  background: linear-gradient(135deg, #ff9500, #ffb347);
                  color: white;
                  padding: 8px 20px;
                  border-radius: 25px;
                  display: inline-block;
                  font-weight: 600;
                  box-shadow: 0 4px 15px rgba(255, 149, 0, 0.3);
                "
              >
                Từ 35,000đ/chiếc
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="offer" class="offer-highlight">
      <div class="container">
        <div class="text-center">
          <h2
            class="scroll-reveal mb-4"
            style="font-size: 2.5rem; font-weight: 700"
          >
            🎁 Ưu Đãi Đặc Biệt
          </h2>

          <!-- Offer Card -->
          <div class="scroll-reveal" style="max-width: 600px; margin: 0 auto">
            <div
              style="
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(10px);
                border-radius: 30px;
                padding: 40px;
                border: 2px solid rgba(255, 255, 255, 0.2);
                position: relative;
                overflow: hidden;
              "
            >
              <!-- Background decoration -->
              <div
                style="
                  position: absolute;
                  top: -50px;
                  right: -50px;
                  width: 150px;
                  height: 150px;
                  background: rgba(255, 255, 255, 0.1);
                  border-radius: 50%;
                  z-index: 1;
                "
              ></div>
              <div
                style="
                  position: absolute;
                  bottom: -30px;
                  left: -30px;
                  width: 100px;
                  height: 100px;
                  background: rgba(255, 255, 255, 0.1);
                  border-radius: 50%;
                  z-index: 1;
                "
              ></div>

              <div style="position: relative; z-index: 2">
                <!-- Price comparison -->
                <div style="margin-bottom: 25px">
                  <div
                    style="
                      display: inline-block;
                      background: rgba(255, 255, 255, 0.2);
                      padding: 8px 20px;
                      border-radius: 25px;
                      margin-bottom: 15px;
                    "
                  >
                    <span
                      style="
                        color: white;
                        opacity: 0.8;
                        text-decoration: line-through;
                        font-size: 1.1rem;
                        margin-right: 10px;
                      "
                      >Giá gốc: 300,000đ</span
                    >
                  </div>
                  <h3
                    style="
                      color: white;
                      font-size: 2.8rem;
                      font-weight: 800;
                      margin: 0;
                      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    "
                  >
                    MIỄN PHÍ
                  </h3>
                  <p
                    style="
                      color: white;
                      font-size: 1.1rem;
                      margin: 10px 0 0 0;
                      opacity: 0.9;
                    "
                  >
                    Chỉ thanh toán phí ship
                    <strong style="font-size: 1.3rem">30,000đ</strong>
                  </p>
                </div>

                <h4
                  style="
                    color: white;
                    font-size: 1.8rem;
                    margin-bottom: 30px;
                    font-weight: 600;
                  "
                >
                  TẶNG HOÀN TOÀN TÚI MẪU CAO CẤP
                </h4>

                <!-- Benefits -->
                <div
                  style="
                    text-align: left;
                    max-width: 400px;
                    margin: 0 auto 30px;
                  "
                >
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      gap: 15px;
                      margin-bottom: 15px;
                    "
                  >
                    <div
                      style="
                        width: 35px;
                        height: 35px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      "
                    >
                      <i
                        class="fas fa-check"
                        style="color: white; font-size: 1rem"
                      ></i>
                    </div>
                    <span style="color: white; font-size: 1.1rem"
                      >Xem chất liệu thực tế</span
                    >
                  </div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      gap: 15px;
                      margin-bottom: 15px;
                    "
                  >
                    <div
                      style="
                        width: 35px;
                        height: 35px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      "
                    >
                      <i
                        class="fas fa-check"
                        style="color: white; font-size: 1rem"
                      ></i>
                    </div>
                    <span style="color: white; font-size: 1.1rem"
                      >Kiểm tra chất lượng in ấn</span
                    >
                  </div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      gap: 15px;
                      margin-bottom: 15px;
                    "
                  >
                    <div
                      style="
                        width: 35px;
                        height: 35px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      "
                    >
                      <i
                        class="fas fa-check"
                        style="color: white; font-size: 1rem"
                      ></i>
                    </div>
                    <span style="color: white; font-size: 1.1rem"
                      >Tư vấn thiết kế miễn phí</span
                    >
                  </div>
                  <div style="display: flex; align-items: center; gap: 15px">
                    <div
                      style="
                        width: 35px;
                        height: 35px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      "
                    >
                      <i
                        class="fas fa-check"
                        style="color: white; font-size: 1rem"
                      ></i>
                    </div>
                    <span style="color: white; font-size: 1.1rem"
                      >Đảm bảo chất lượng 100%</span
                    >
                  </div>
                </div>

                <!-- Limited time -->
                <div
                  style="
                    background: rgba(255, 255, 255, 0.2);
                    padding: 15px 25px;
                    border-radius: 20px;
                    margin-bottom: 30px;
                  "
                >
                  <p
                    style="
                      color: white;
                      margin: 0;
                      font-size: 1rem;
                      font-weight: 600;
                    "
                  >
                    ⏰ Ưu đãi có giới hạn - Chỉ dành cho 100 khách hàng đầu tiên
                  </p>
                </div>

                <a
                  href="#bao-gia"
                  class="btn"
                  style="
                    background: white;
                    color: #ff9500;
                    font-size: 1.3rem;
                    padding: 18px 45px;
                    border-radius: 50px;
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
                    border: none;
                    font-weight: 700;
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: hidden;
                  "
                >
                  <i class="fas fa-gift" style="margin-right: 10px"></i> Nhận
                  Túi Mẫu Ngay
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="bao-gia" class="section">
      <div class="container">
        <div class="row">
          <div class="col col-6">
            <div class="scroll-reveal">
              <h2>Nhận Báo Giá Chi Tiết</h2>
              <p>
                Điền thông tin để nhận báo giá chính xác nhất cho đơn hàng của
                bạn
              </p>

              <div class="modern-card mt-4">
                <div class="d-flex align-center gap-3">
                  <div
                    class="feature-icon-blue"
                    style="width: 70px; height: 70px; margin: 0"
                  >
                    <i class="fas fa-shipping-fast"></i>
                  </div>
                  <div>
                    <h4 class="mb-1" style="color: #1a1a1a; font-weight: 600">
                      Giao Hàng Nhanh
                    </h4>
                    <p class="mb-0" style="color: #4a5568">
                      <span class="orange-accent">3-5 ngày</span> làm việc toàn
                      quốc
                    </p>
                  </div>
                </div>
              </div>

              <div class="modern-card mt-3">
                <div class="d-flex align-center gap-3">
                  <div
                    class="feature-icon-orange"
                    style="width: 70px; height: 70px; margin: 0"
                  >
                    <i class="fas fa-shield-alt"></i>
                  </div>
                  <div>
                    <h4 class="mb-1" style="color: #1a1a1a; font-weight: 600">
                      Cam Kết Chất Lượng
                    </h4>
                    <p class="mb-0" style="color: #4a5568">
                      Đổi mới <span class="orange-accent">100%</span> nếu không
                      hài lòng
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col col-6">
            <div class="modern-card scroll-reveal">
              <form id="quote-form">
                <div class="form-group">
                  <label class="form-label">Họ và tên *</label>
                  <input
                    type="text"
                    class="form-input"
                    name="name"
                    required
                    placeholder="Nhập họ tên của bạn"
                  />
                </div>

                <div class="form-group">
                  <label class="form-label">Số điện thoại *</label>
                  <input
                    type="tel"
                    class="form-input"
                    name="phone"
                    required
                    placeholder="Nhập số điện thoại"
                  />
                </div>

                <div class="form-group">
                  <label class="form-label">Email</label>
                  <input
                    type="email"
                    class="form-input"
                    name="email"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div class="row">
                  <div class="col col-6">
                    <div class="form-group">
                      <label class="form-label">Loại túi *</label>
                      <select class="form-select" name="bag_type" required>
                        <option value="">Chọn loại túi</option>
                        <option value="tote">Túi Tote Canvas</option>
                        <option value="cotton">Túi Cotton Eco</option>
                        <option value="quai">Túi Vải Có Quai</option>
                        <option value="premium">Túi Premium</option>
                      </select>
                    </div>
                  </div>
                  <div class="col col-6">
                    <div class="form-group">
                      <label class="form-label">Số lượng *</label>
                      <select class="form-select" name="quantity" required>
                        <option value="">Chọn số lượng</option>
                        <option value="50-100">50-100 túi</option>
                        <option value="100-300">100-300 túi</option>
                        <option value="300-500">300-500 túi</option>
                        <option value="500+">Trên 500 túi</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="form-group">
                  <label class="form-label">Yêu cầu đặc biệt</label>
                  <textarea
                    class="form-textarea"
                    name="requirements"
                    placeholder="Mô tả về thiết kế, kích thước, màu sắc..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  class="btn btn-primary"
                  style="width: 100%; background: var(--primary-color)"
                >
                  <i class="fas fa-paper-plane"></i> Gửi Yêu Cầu Báo Giá
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="lien-he" class="section" style="background: #f8f9fa">
      <div class="container">
        <div class="text-center mb-5">
          <h2 class="scroll-reveal">Liên Hệ Với Chúng Tôi</h2>
          <p class="scroll-reveal">Sẵn sàng hỗ trợ bạn 24/7</p>
        </div>

        <div class="row">
          <div class="col col-4">
            <div class="modern-card text-center scroll-reveal">
              <div
                class="card-icon"
                style="
                  background: linear-gradient(135deg, #25d366, #20b759);
                  width: 80px;
                  height: 80px;
                  margin: 0 auto 20px;
                  border-radius: 20px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  box-shadow: 0 10px 30px rgba(37, 211, 102, 0.3);
                "
              >
                <i
                  class="fab fa-whatsapp"
                  style="font-size: 1.8rem; color: white"
                ></i>
              </div>
              <h4 style="color: #1a1a1a; font-weight: 600">WhatsApp</h4>
              <p style="color: #4a5568">Liên hệ nhanh qua WhatsApp</p>
              <a href="https://wa.me/84123456789" class="btn btn-success"
                >Chat Ngay</a
              >
            </div>
          </div>

          <div class="col col-4">
            <div class="modern-card text-center scroll-reveal">
              <div
                class="card-icon"
                style="
                  background: linear-gradient(135deg, #4299e1, #667eea);
                  width: 80px;
                  height: 80px;
                  margin: 0 auto 20px;
                  border-radius: 20px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  box-shadow: 0 10px 30px rgba(66, 153, 225, 0.3);
                "
              >
                <i
                  class="fab fa-facebook-messenger"
                  style="font-size: 1.8rem; color: white"
                ></i>
              </div>
              <h4 style="color: #1a1a1a; font-weight: 600">Facebook</h4>
              <p style="color: #4a5568">Nhắn tin qua Facebook</p>
              <a href="#" class="btn btn-primary">Messenger</a>
            </div>
          </div>

          <div class="col col-4">
            <div class="modern-card text-center scroll-reveal">
              <div
                class="card-icon"
                style="
                  background: linear-gradient(135deg, #ff9500, #ffb347);
                  width: 80px;
                  height: 80px;
                  margin: 0 auto 20px;
                  border-radius: 20px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  box-shadow: 0 10px 30px rgba(255, 149, 0, 0.3);
                "
              >
                <i
                  class="fas fa-phone"
                  style="font-size: 1.8rem; color: white"
                ></i>
              </div>
              <h4 style="color: #1a1a1a; font-weight: 600">Hotline</h4>
              <p style="color: #4a5568">Gọi trực tiếp cho chúng tôi</p>
              <a href="tel:0123456789" class="btn btn-secondary"
                >0123 456 789</a
              >
            </div>
          </div>
        </div>
      </div>
    </section>

    <footer
      style="
        background: linear-gradient(
          135deg,
          #1a202c 0%,
          #2d3748 50%,
          #4a5568 100%
        );
        padding: 80px 0 30px;
        position: relative;
        overflow: hidden;
      "
    >
      <!-- Background decoration -->
      <div
        style="
          position: absolute;
          top: -100px;
          right: -100px;
          width: 300px;
          height: 300px;
          background: linear-gradient(
            135deg,
            rgba(255, 149, 0, 0.1),
            rgba(255, 179, 71, 0.05)
          );
          border-radius: 50%;
          z-index: 1;
        "
      ></div>
      <div
        style="
          position: absolute;
          bottom: -150px;
          left: -150px;
          width: 400px;
          height: 400px;
          background: linear-gradient(
            135deg,
            rgba(66, 153, 225, 0.1),
            rgba(102, 126, 234, 0.05)
          );
          border-radius: 50%;
          z-index: 1;
        "
      ></div>

      <div class="container" style="position: relative; z-index: 2">
        <div class="row">
          <div class="col col-4">
            <div style="margin-bottom: 40px">
              <h3
                style="
                  color: white;
                  margin-bottom: 25px;
                  font-size: 1.8rem;
                  font-weight: 700;
                  display: flex;
                  align-items: center;
                  gap: 15px;
                "
              >
                <div
                  style="
                    width: 50px;
                    height: 50px;
                    background: linear-gradient(135deg, #ff9500, #ffb347);
                    border-radius: 15px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  <i
                    class="fas fa-shopping-bag"
                    style="color: white; font-size: 1.2rem"
                  ></i>
                </div>
                Loma Bag
              </h3>
              <p
                style="
                  color: #cbd5e0;
                  line-height: 1.7;
                  font-size: 1rem;
                  margin-bottom: 25px;
                "
              >
                Xưởng sản xuất túi vải in logo chuyên nghiệp hàng đầu Việt Nam,
                phục vụ các shop online và thương hiệu trên toàn quốc với chất
                lượng cao nhất.
              </p>

              <!-- Social media -->
              <div style="display: flex; gap: 15px">
                <a
                  href="#"
                  style="
                    width: 45px;
                    height: 45px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #cbd5e0;
                    text-decoration: none;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(10px);
                  "
                >
                  <i class="fab fa-facebook-f" style="font-size: 1.1rem"></i>
                </a>
                <a
                  href="#"
                  style="
                    width: 45px;
                    height: 45px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #cbd5e0;
                    text-decoration: none;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(10px);
                  "
                >
                  <i class="fab fa-instagram" style="font-size: 1.1rem"></i>
                </a>
                <a
                  href="#"
                  style="
                    width: 45px;
                    height: 45px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #cbd5e0;
                    text-decoration: none;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(10px);
                  "
                >
                  <i class="fab fa-zalo" style="font-size: 1.1rem"></i>
                </a>
              </div>
            </div>
          </div>

          <div class="col col-4">
            <div style="margin-bottom: 40px">
              <h4
                style="
                  color: white;
                  margin-bottom: 25px;
                  font-size: 1.3rem;
                  font-weight: 600;
                "
              >
                Liên Hệ
              </h4>
              <div style="space-y: 15px">
                <div
                  style="
                    display: flex;
                    align-items: flex-start;
                    gap: 15px;
                    margin-bottom: 15px;
                  "
                >
                  <div
                    style="
                      width: 40px;
                      height: 40px;
                      background: rgba(66, 153, 225, 0.2);
                      border-radius: 10px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      flex-shrink: 0;
                    "
                  >
                    <i
                      class="fas fa-map-marker-alt"
                      style="color: #4299e1; font-size: 1rem"
                    ></i>
                  </div>
                  <div>
                    <p style="color: #cbd5e0; margin: 0; line-height: 1.5">
                      123 Đường ABC, Phường XYZ<br />Quận 1, TP. Hồ Chí Minh
                    </p>
                  </div>
                </div>

                <div
                  style="
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    margin-bottom: 15px;
                  "
                >
                  <div
                    style="
                      width: 40px;
                      height: 40px;
                      background: rgba(255, 149, 0, 0.2);
                      border-radius: 10px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                    "
                  >
                    <i
                      class="fas fa-phone"
                      style="color: #ff9500; font-size: 1rem"
                    ></i>
                  </div>
                  <p style="color: #cbd5e0; margin: 0; font-weight: 600">
                    0123 456 789
                  </p>
                </div>

                <div style="display: flex; align-items: center; gap: 15px">
                  <div
                    style="
                      width: 40px;
                      height: 40px;
                      background: rgba(72, 187, 120, 0.2);
                      border-radius: 10px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                    "
                  >
                    <i
                      class="fas fa-envelope"
                      style="color: #48bb78; font-size: 1rem"
                    ></i>
                  </div>
                  <p style="color: #cbd5e0; margin: 0"><EMAIL></p>
                </div>
              </div>
            </div>
          </div>

          <div class="col col-4">
            <div style="margin-bottom: 40px">
              <h4
                style="
                  color: white;
                  margin-bottom: 25px;
                  font-size: 1.3rem;
                  font-weight: 600;
                "
              >
                Dịch Vụ
              </h4>
              <ul style="list-style: none; padding: 0; margin: 0">
                <li style="margin-bottom: 12px">
                  <a
                    href="#san-pham"
                    style="
                      color: #cbd5e0;
                      text-decoration: none;
                      display: flex;
                      align-items: center;
                      gap: 10px;
                      transition: all 0.3s ease;
                      padding: 8px 0;
                    "
                  >
                    <i
                      class="fas fa-chevron-right"
                      style="color: #ff9500; font-size: 0.8rem"
                    ></i>
                    Túi vải in logo
                  </a>
                </li>
                <li style="margin-bottom: 12px">
                  <a
                    href="#bao-gia"
                    style="
                      color: #cbd5e0;
                      text-decoration: none;
                      display: flex;
                      align-items: center;
                      gap: 10px;
                      transition: all 0.3s ease;
                      padding: 8px 0;
                    "
                  >
                    <i
                      class="fas fa-chevron-right"
                      style="color: #ff9500; font-size: 0.8rem"
                    ></i>
                    Thiết kế túi canvas
                  </a>
                </li>
                <li style="margin-bottom: 12px">
                  <a
                    href="#gioi-thieu"
                    style="
                      color: #cbd5e0;
                      text-decoration: none;
                      display: flex;
                      align-items: center;
                      gap: 10px;
                      transition: all 0.3s ease;
                      padding: 8px 0;
                    "
                  >
                    <i
                      class="fas fa-chevron-right"
                      style="color: #ff9500; font-size: 0.8rem"
                    ></i>
                    Sản xuất túi cotton
                  </a>
                </li>
                <li style="margin-bottom: 12px">
                  <a
                    href="#lien-he"
                    style="
                      color: #cbd5e0;
                      text-decoration: none;
                      display: flex;
                      align-items: center;
                      gap: 10px;
                      transition: all 0.3s ease;
                      padding: 8px 0;
                    "
                  >
                    <i
                      class="fas fa-chevron-right"
                      style="color: #ff9500; font-size: 0.8rem"
                    ></i>
                    Tư vấn miễn phí
                  </a>
                </li>
              </ul>

              <!-- Newsletter -->
              <div
                style="
                  margin-top: 30px;
                  padding: 25px;
                  background: rgba(255, 255, 255, 0.05);
                  border-radius: 20px;
                  backdrop-filter: blur(10px);
                  border: 1px solid rgba(255, 255, 255, 0.1);
                "
              >
                <h5
                  style="
                    color: white;
                    margin-bottom: 15px;
                    font-size: 1.1rem;
                    font-weight: 600;
                  "
                >
                  📧 Nhận Ưu Đãi
                </h5>
                <p
                  style="color: #cbd5e0; font-size: 0.9rem; margin-bottom: 15px"
                >
                  Đăng ký để nhận thông tin khuyến mãi mới nhất
                </p>
                <div style="display: flex; gap: 10px">
                  <input
                    type="email"
                    placeholder="Email của bạn"
                    style="
                      flex: 1;
                      padding: 12px 15px;
                      border: 1px solid rgba(255, 255, 255, 0.2);
                      border-radius: 10px;
                      background: rgba(255, 255, 255, 0.1);
                      color: white;
                      font-size: 0.9rem;
                    "
                  />
                  <button
                    style="
                      padding: 12px 15px;
                      background: linear-gradient(135deg, #ff9500, #ffb347);
                      border: none;
                      border-radius: 10px;
                      color: white;
                      font-weight: 600;
                      cursor: pointer;
                    "
                  >
                    <i class="fas fa-paper-plane"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Bottom section -->
        <div
          style="
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: 50px;
            padding-top: 30px;
          "
        >
          <div class="row align-center">
            <div class="col col-6">
              <p style="color: #a0aec0; margin: 0; font-size: 0.9rem">
                &copy; 2024 <strong style="color: #ff9500">Loma Bag</strong>.
                Tất cả quyền được bảo lưu.
              </p>
            </div>
            <div class="col col-6" style="text-align: right">
              <div style="display: flex; justify-content: flex-end; gap: 25px">
                <a
                  href="#"
                  style="
                    color: #a0aec0;
                    text-decoration: none;
                    font-size: 0.9rem;
                    transition: all 0.3s ease;
                  "
                  >Chính sách bảo mật</a
                >
                <a
                  href="#"
                  style="
                    color: #a0aec0;
                    text-decoration: none;
                    font-size: 0.9rem;
                    transition: all 0.3s ease;
                  "
                  >Điều khoản dịch vụ</a
                >
                <a
                  href="#"
                  style="
                    color: #a0aec0;
                    text-decoration: none;
                    font-size: 0.9rem;
                    transition: all 0.3s ease;
                  "
                  >Hỗ trợ</a
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <script src="assets/js/common.js"></script>

    <script>
      document
        .getElementById("quote-form")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          showNotification(
            "Cảm ơn bạn! Chúng tôi sẽ liên hệ trong vòng 30 phút.",
            "success"
          );
          this.reset();
        });
    </script>
  </body>
</html>
