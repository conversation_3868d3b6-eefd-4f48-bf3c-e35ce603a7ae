<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loma - Xưởng <PERSON><PERSON><PERSON> In Logo <PERSON></title>
    <link rel="stylesheet" href="assets/css/common.css">
    <style>
        .hero-loma {
            min-height: 100vh;
            display: flex;
            align-items: center;
            background: 
                radial-gradient(ellipse at top left, rgba(255, 204, 128, 0.3) 0%, transparent 50%),
                radial-gradient(ellipse at bottom right, rgba(255, 167, 38, 0.3) 0%, transparent 50%),
                #ffffff;
            position: relative;
            overflow: hidden;
            padding: 80px 0;
        }

        .hero-loma::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(ellipse at top left, rgba(255, 204, 128, 0.15) 0%, transparent 40%),
                radial-gradient(ellipse at bottom right, rgba(255, 167, 38, 0.15) 0%, transparent 40%);
            animation: float 20s ease-in-out infinite;
        }

        .hero-content-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
        }

        .hero-text {
            color: #1a202c;
        }

        .hero-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: #1a202c;
        }

        .hero-title .highlight {
            background: linear-gradient(45deg, #ff8f00, #ff6f00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-description {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #2d3748;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .btn-quote {
            background: linear-gradient(45deg, #1a202c, #2d3748);
            color: white;
            padding: 18px 35px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 8px 25px rgba(26, 32, 44, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-quote::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-quote:hover::before {
            left: 100%;
        }

        .btn-quote:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(26, 32, 44, 0.4);
        }

        .btn-offer {
            background: rgba(255, 143, 0, 0.15);
            color: #ff6f00;
            padding: 18px 35px;
            border: 2px solid rgba(255, 143, 0, 0.3);
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .btn-offer:hover {
            background: rgba(255, 143, 0, 0.25);
            border-color: rgba(255, 143, 0, 0.5);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 143, 0, 0.2);
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
        }

        .stat-item {
            text-align: center;
            color: #1a202c;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #ff8f00, #ff6f00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 1rem;
            color: #2d3748;
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
        }

        .product-image {
            width: 100%;
            max-width: 500px;
            height: auto;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
            transition: transform 0.3s ease;
        }

        .product-image:hover {
            transform: scale(1.02);
        }

        .floating-icon {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            z-index: 5;
        }

        .icon-1 {
            top: 15%;
            left: 5%;
            animation: float-1 6s ease-in-out infinite;
            color: #38a169;
        }

        .icon-2 {
            top: 5%;
            right: 15%;
            animation: float-2 8s ease-in-out infinite;
            color: #3182ce;
        }

        .icon-3 {
            bottom: 15%;
            left: 15%;
            animation: float-3 7s ease-in-out infinite;
            color: #d69e2e;
        }

        .icon-4 {
            bottom: 5%;
            right: 5%;
            animation: float-4 9s ease-in-out infinite;
            color: #e53e3e;
        }



        @keyframes float-1 {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(5deg); }
            66% { transform: translateY(-10px) rotate(-3deg); }
        }

        @keyframes float-2 {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-15px) rotate(-5deg); }
            75% { transform: translateY(-25px) rotate(3deg); }
        }

        @keyframes float-3 {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            40% { transform: translateY(-18px) rotate(4deg); }
            80% { transform: translateY(-8px) rotate(-2deg); }
        }

        @keyframes float-4 {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            30% { transform: translateY(-22px) rotate(-4deg); }
            70% { transform: translateY(-12px) rotate(6deg); }
        }



        /* Responsive Design */
        @media (max-width: 968px) {
            .hero-content-wrapper {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }

            .product-image {
                max-width: 400px;
            }

            .hero-stats {
                grid-template-columns: repeat(3, 1fr);
                gap: 20px;
            }

            .stat-number {
                font-size: 2rem;
            }

            .floating-icon {
                width: 45px;
                height: 45px;
                font-size: 1.1rem;
            }
        }

        @media (max-width: 768px) {
            .hero-buttons {
                justify-content: center;
            }

            .btn-quote, .btn-offer {
                padding: 15px 25px;
                font-size: 1rem;
            }

            .floating-icon {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .product-image {
                max-width: 350px;
            }

            .hero-content-wrapper {
                gap: 30px;
            }
        }

        @media (max-width: 480px) {
            .hero-stats {
                gap: 15px;
            }

            .stat-number {
                font-size: 1.8rem;
            }

            .stat-label {
                font-size: 0.9rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <section class="hero-loma">
        <div class="container">
            <div class="hero-content-wrapper">
                <div class="hero-text">
                    <h1 class="hero-title">
                        Túi Vải Chất Lượng Cao cho <span class="highlight">Thương Hiệu</span> Của Bạn
                    </h1>
                    <p class="hero-description">
                        Loma - Xưởng sản xuất túi vải in logo chuyên nghiệp với chất lượng cao, 
                        thiết kế đẹp mắt và giá cả cạnh tranh. Đội ngũ nhiều năm kinh nghiệm, 
                        cam kết mang đến sản phẩm tốt nhất cho thương hiệu của bạn.
                    </p>
                    
                    <div class="hero-buttons">
                        <a href="#contact" class="btn-quote">
                            📞 Nhận Báo Giá
                        </a>
                        <a href="#offers" class="btn-offer">
                            🎁 Nhận Ưu Đãi
                        </a>
                    </div>

                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">Thương hiệu tin tưởng</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50k+</div>
                            <div class="stat-label">Túi vải đã sản xuất</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">98%</div>
                            <div class="stat-label">Khách hàng hài lòng</div>
                        </div>
                    </div>
                </div>

                <div class="hero-visual">
                    <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/hero-landing-page-banner/_Beige%20Minimalist%20New%20Product%20Beauty%20Serum%20Facebook%20Post.jpg" 
                         alt="Loma - Túi vải chất lượng cao" 
                         style="width: 100% !important; max-width: 500px !important; height: auto !important; border-radius: 20px !important; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1) !important; position: relative !important; z-index: 1 !important; transition: transform 0.3s ease !important; display: block !important;">
                    
                    <!-- Floating Icons -->
                    <div class="floating-icon icon-1">🛍️</div>
                    <div class="floating-icon icon-2">⭐</div>
                    <div class="floating-icon icon-3">📦</div>
                    <div class="floating-icon icon-4">💯</div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Smooth scroll for buttons
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add entrance animations
        window.addEventListener('load', function() {
            const heroText = document.querySelector('.hero-text');
            const heroVisual = document.querySelector('.hero-visual');
            
            heroText.style.opacity = '0';
            heroText.style.transform = 'translateY(30px)';
            heroVisual.style.opacity = '0';
            heroVisual.style.transform = 'translateX(30px)';
            
            setTimeout(() => {
                heroText.style.transition = 'all 0.8s ease';
                heroText.style.opacity = '1';
                heroText.style.transform = 'translateY(0)';
            }, 200);
            
            setTimeout(() => {
                heroVisual.style.transition = 'all 0.8s ease';
                heroVisual.style.opacity = '1';
                heroVisual.style.transform = 'translateX(0)';
            }, 400);
        });
    </script>
</body>
</html>
