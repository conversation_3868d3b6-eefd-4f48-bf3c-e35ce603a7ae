<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> & <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
    </style>
</head>
<body>
    <section style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 80px 0;
        position: relative;
        overflow: hidden;
    ">
        <!-- Background decorative elements -->
        <div style="
            position: absolute;
            top: -50px;
            right: -50px;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        "></div>
        <div style="
            position: absolute;
            bottom: -30px;
            left: -30px;
            width: 150px;
            height: 150px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite reverse;
        "></div>

        <div class="container">
            <!-- Header Section -->
            <div style="
                text-align: center;
                margin-bottom: 60px;
                position: relative;
                z-index: 2;
            ">
                <h1 style="
                    font-size: clamp(2.5rem, 5vw, 4rem);
                    font-weight: 800;
                    color: white;
                    margin-bottom: 20px;
                    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                    line-height: 1.1;
                ">
                    Cam Kết <span style="
                        background: linear-gradient(45deg, #ffd700, #ff8c00);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    ">Chất Lượng</span> Loma
                </h1>
                <p style="
                    font-size: 1.2rem;
                    color: rgba(255, 255, 255, 0.9);
                    max-width: 600px;
                    margin: 0 auto;
                    line-height: 1.6;
                    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                ">
                    Với hơn 5 năm kinh nghiệm, chúng tôi cam kết mang đến sản phẩm chất lượng cao và dịch vụ tận tâm nhất
                </p>
            </div>

            <!-- Main Content Grid -->
            <div style="
                display: grid;
                grid-template-columns: 1.2fr 0.8fr;
                gap: 50px;
                margin-bottom: 60px;
                align-items: start;
            ">
                <!-- Left: Policy List -->
                <div style="
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(20px);
                    border-radius: 25px;
                    padding: 40px;
                    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    position: relative;
                    overflow: hidden;
                ">
                    <div style="
                        position: absolute;
                        top: -20px;
                        right: -20px;
                        width: 100px;
                        height: 100px;
                        background: linear-gradient(45deg, #667eea, #764ba2);
                        border-radius: 50%;
                        opacity: 0.1;
                    "></div>

                    <h2 style="
                        font-size: 2rem;
                        font-weight: 700;
                        color: #1a202c;
                        margin-bottom: 30px;
                        position: relative;
                        z-index: 2;
                    ">
                        🏆 Cam Kết Chất Lượng Loma
                    </h2>

                    <ul style="
                        list-style: none;
                        padding: 0;
                        margin: 0;
                        position: relative;
                        z-index: 2;
                    ">
                        <li style="
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            padding: 18px 0;
                            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
                            transition: all 0.3s ease;
                        " onmouseover="this.style.paddingLeft='10px'; this.style.backgroundColor='rgba(102, 126, 234, 0.05)'" onmouseout="this.style.paddingLeft='0'; this.style.backgroundColor='transparent'">
                            <div style="
                                background: linear-gradient(45deg, #667eea, #764ba2);
                                color: white;
                                width: 35px;
                                height: 35px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 1.2rem;
                                flex-shrink: 0;
                                box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
                            ">
                                ⭐
                            </div>
                            <span style="
                                font-size: 1.1rem;
                                font-weight: 600;
                                color: #1a202c;
                                line-height: 1.5;
                            ">
                                Hơn 5 năm kinh nghiệm trong ngành sản xuất túi vải
                            </span>
                        </li>

                        <li style="
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            padding: 18px 0;
                            border-bottom: 1px solid rgba(255, 140, 0, 0.1);
                            transition: all 0.3s ease;
                        " onmouseover="this.style.paddingLeft='10px'; this.style.backgroundColor='rgba(255, 140, 0, 0.05)'" onmouseout="this.style.paddingLeft='0'; this.style.backgroundColor='transparent'">
                            <div style="
                                background: linear-gradient(45deg, #ff8c00, #ffd700);
                                color: white;
                                width: 35px;
                                height: 35px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 1.2rem;
                                flex-shrink: 0;
                                box-shadow: 0 5px 15px rgba(255, 140, 0, 0.3);
                            ">
                                🔍
                            </div>
                            <span style="
                                font-size: 1.1rem;
                                font-weight: 600;
                                color: #1a202c;
                                line-height: 1.5;
                            ">
                                Kiểm soát chất lượng nghiêm ngặt từng sản phẩm
                            </span>
                        </li>

                        <li style="
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            padding: 18px 0;
                            border-bottom: 1px solid rgba(56, 161, 105, 0.1);
                            transition: all 0.3s ease;
                        " onmouseover="this.style.paddingLeft='10px'; this.style.backgroundColor='rgba(56, 161, 105, 0.05)'" onmouseout="this.style.paddingLeft='0'; this.style.backgroundColor='transparent'">
                            <div style="
                                background: linear-gradient(45deg, #38a169, #48bb78);
                                color: white;
                                width: 35px;
                                height: 35px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 1.2rem;
                                flex-shrink: 0;
                                box-shadow: 0 5px 15px rgba(56, 161, 105, 0.3);
                            ">
                                🚚
                            </div>
                            <span style="
                                font-size: 1.1rem;
                                font-weight: 600;
                                color: #1a202c;
                                line-height: 1.5;
                            ">
                                Miễn phí vận chuyển toàn quốc
                            </span>
                        </li>

                        <li style="
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            padding: 18px 0;
                            border-bottom: 1px solid rgba(214, 158, 46, 0.1);
                            transition: all 0.3s ease;
                        " onmouseover="this.style.paddingLeft='10px'; this.style.backgroundColor='rgba(214, 158, 46, 0.05)'" onmouseout="this.style.paddingLeft='0'; this.style.backgroundColor='transparent'">
                            <div style="
                                background: linear-gradient(45deg, #d69e2e, #f6e05e);
                                color: white;
                                width: 35px;
                                height: 35px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 1.2rem;
                                flex-shrink: 0;
                                box-shadow: 0 5px 15px rgba(214, 158, 46, 0.3);
                            ">
                                📦
                            </div>
                            <span style="
                                font-size: 1.1rem;
                                font-weight: 600;
                                color: #1a202c;
                                line-height: 1.5;
                            ">
                                Đóng thùng carton khi giao hàng đảm bảo túi được giữ form dáng tốt nhất
                            </span>
                        </li>

                        <li style="
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            padding: 18px 0;
                            border-bottom: 1px solid rgba(245, 101, 0, 0.1);
                            transition: all 0.3s ease;
                        " onmouseover="this.style.paddingLeft='10px'; this.style.backgroundColor='rgba(245, 101, 0, 0.05)'" onmouseout="this.style.paddingLeft='0'; this.style.backgroundColor='transparent'">
                            <div style="
                                background: linear-gradient(45deg, #f56500, #ff8500);
                                color: white;
                                width: 35px;
                                height: 35px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 1.2rem;
                                flex-shrink: 0;
                                box-shadow: 0 5px 15px rgba(245, 101, 0, 0.3);
                            ">
                                🔄
                            </div>
                            <span style="
                                font-size: 1.1rem;
                                font-weight: 600;
                                color: #1a202c;
                                line-height: 1.5;
                            ">
                                Bảo hành đổi trả 30 ngày
                            </span>
                        </li>

                        <li style="
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            padding: 18px 0 0 0;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.paddingLeft='10px'; this.style.backgroundColor='rgba(76, 81, 191, 0.05)'" onmouseout="this.style.paddingLeft='0'; this.style.backgroundColor='transparent'">
                            <div style="
                                background: linear-gradient(45deg, #4c51bf, #553c9a);
                                color: white;
                                width: 35px;
                                height: 35px;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 1.2rem;
                                flex-shrink: 0;
                                box-shadow: 0 5px 15px rgba(76, 81, 191, 0.3);
                            ">
                                🏭
                            </div>
                            <span style="
                                font-size: 1.1rem;
                                font-weight: 600;
                                color: #1a202c;
                                line-height: 1.5;
                            ">
                                Xưởng sản xuất với quy trình kiểm soát nghiêm ngặt
                            </span>
                        </li>
                    </ul>
                </div>

                <!-- Right: Single Factory Image -->
                <div style="
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(20px);
                    border-radius: 25px;
                    padding: 30px;
                    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    position: relative;
                    overflow: hidden;
                ">
                    <div style="
                        position: absolute;
                        top: -20px;
                        left: -20px;
                        width: 80px;
                        height: 80px;
                        background: linear-gradient(45deg, #ff8c00, #ffd700);
                        border-radius: 50%;
                        opacity: 0.1;
                    "></div>

                    <div style="
                        position: relative;
                        border-radius: 20px;
                        overflow: hidden;
                        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
                        transition: all 0.3s ease;
                        z-index: 2;
                    " onmouseover="this.style.transform='scale(1.02)'; this.style.boxShadow='0 25px 50px rgba(0, 0, 0, 0.2)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'">
                        <img src="https://supabase.mooly.vn/storage/v1/object/public/product-loma/xuong-may/xuong-san-xuat-tui-vai-loma-bag-1.jpg" 
                             alt="Xưởng sản xuất Loma Bag"
                             style="
                                 width: 100%;
                                 height: 400px;
                                 object-fit: cover;
                                 display: block;
                             ">
                        <div style="
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
                            color: white;
                            padding: 25px;
                            text-align: center;
                        ">
                            <h3 style="
                                font-size: 1.4rem;
                                font-weight: 700;
                                margin-bottom: 8px;
                                text-shadow: 0 2px 10px rgba(0, 0, 0, 0.8);
                            ">
                                🏭 Xưởng Sản Xuất Loma
                            </h3>
                            <p style="
                                font-size: 1rem;
                                opacity: 0.95;
                                line-height: 1.5;
                                text-shadow: 0 1px 5px rgba(0, 0, 0, 0.8);
                            ">
                                Dây chuyền sản xuất hiện đại với quy trình nghiêm ngặt, 
                                đảm bảo chất lượng tốt nhất cho từng sản phẩm
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom CTA Section -->
            <div style="
                text-align: center;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-radius: 25px;
                padding: 40px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                position: relative;
                overflow: hidden;
            ">
                <div style="
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 300px;
                    height: 300px;
                    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
                    border-radius: 50%;
                "></div>

                <h2 style="
                    font-size: 2.2rem;
                    font-weight: 700;
                    color: white;
                    margin-bottom: 15px;
                    position: relative;
                    z-index: 2;
                ">
                    Sẵn Sàng Đặt Hàng?
                </h2>
                <p style="
                    font-size: 1.1rem;
                    color: rgba(255, 255, 255, 0.9);
                    margin-bottom: 30px;
                    max-width: 500px;
                    margin-left: auto;
                    margin-right: auto;
                    position: relative;
                    z-index: 2;
                ">
                    Liên hệ ngay để được tư vấn miễn phí và nhận báo giá tốt nhất
                </p>
                
                <div style="
                    display: flex;
                    gap: 20px;
                    justify-content: center;
                    flex-wrap: wrap;
                    position: relative;
                    z-index: 2;
                ">
                    <button style="
                        background: linear-gradient(45deg, #667eea, #764ba2);
                        color: white;
                        padding: 15px 30px;
                        border: none;
                        border-radius: 50px;
                        font-size: 1.1rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
                        position: relative;
                        overflow: hidden;
                        min-width: 220px;
                    " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 12px 35px rgba(102, 126, 234, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 25px rgba(102, 126, 234, 0.3)'">
                        🎁 Nhận Mẫu Túi Miễn Phí
                    </button>
                    
                    <button style="
                        background: linear-gradient(45deg, #ff8c00, #ffd700);
                        color: white;
                        padding: 15px 30px;
                        border: none;
                        border-radius: 50px;
                        font-size: 1.1rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        box-shadow: 0 8px 25px rgba(255, 140, 0, 0.3);
                        position: relative;
                        overflow: hidden;
                        min-width: 200px;
                    " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 12px 35px rgba(255, 140, 0, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 25px rgba(255, 140, 0, 0.3)'">
                        💰 Yêu Cầu Báo Giá
                    </button>
                </div>
            </div>
        </div>
    </section>

    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            section {
                padding: 60px 0 !important;
            }

            [style*="grid-template-columns: 1.2fr 0.8fr"] {
                grid-template-columns: 1fr !important;
                gap: 30px !important;
            }

            [style*="padding: 40px"] {
                padding: 25px !important;
            }

            [style*="padding: 30px"] {
                padding: 20px !important;
            }

            [style*="font-size: clamp(2.5rem, 5vw, 4rem)"] {
                font-size: 2.2rem !important;
            }

            [style*="font-size: 2rem"] {
                font-size: 1.6rem !important;
            }

            [style*="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap"] {
                flex-direction: column !important;
                align-items: center !important;
            }

            [style*="min-width: 200px"], [style*="min-width: 220px"] {
                width: 100% !important;
                max-width: 280px !important;
            }

            [style*="height: 400px"] {
                height: 300px !important;
            }
        }

        @media (max-width: 480px) {
            [style*="padding: 25px"] {
                padding: 20px !important;
            }

            [style*="padding: 20px"] {
                padding: 15px !important;
            }

            [style*="font-size: 2.2rem"] {
                font-size: 1.8rem !important;
            }

            [style*="font-size: 1.6rem"] {
                font-size: 1.4rem !important;
            }

            [style*="gap: 50px"] {
                gap: 25px !important;
            }

            [style*="height: 300px"] {
                height: 250px !important;
            }

            [style*="font-size: 1.1rem"] {
                font-size: 1rem !important;
            }

            [style*="padding: 18px 0"] {
                padding: 15px 0 !important;
            }
        }
    </style>
</body>
</html>