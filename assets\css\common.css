/* ========== RESET & BASE STYLES ========== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* ========== TYPOGRAPHY ========== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: #1a1a1a;
    font-weight: 800;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    color: #1a1a1a;
    font-weight: 700;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    color: #2d3748;
    font-weight: 600;
}

p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: #4a5568;
    line-height: 1.7;
}

/* ========== LAYOUT COMPONENTS ========== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section {
    padding: 80px 0;
}

.section-padding {
    padding: 60px 0;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: -15px;
}

.col {
    padding: 15px;
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-12 { flex: 0 0 100%; }

/* ========== HEADER & NAVIGATION ========== */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 15px 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #667eea;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #667eea;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #667eea;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* ========== BUTTONS ========== */
.btn {
    display: inline-block;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #4299e1 0%, #667eea 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(66, 153, 225, 0.4);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.6);
    background: linear-gradient(135deg, #3182ce 0%, #5a67d8 100%);
}

.btn-secondary {
    background: white;
    color: #4299e1;
    border: 2px solid #4299e1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: #4299e1;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(86, 171, 47, 0.4);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(86, 171, 47, 0.6);
}

/* ========== CARDS ========== */
.card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #ff9500 0%, #ffb347 100%);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff9500 0%, #ffb347 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 25px rgba(255, 149, 0, 0.3);
}

/* ========== FORMS ========== */
.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

/* ========== HERO SECTION ========== */
.hero {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    color: #1a1a1a;
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: cover;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero h1 {
    color: #1a1a1a;
    margin-bottom: 20px;
}

.hero p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    color: #4a5568;
    font-weight: 400;
}

/* ========== ANIMATIONS ========== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.8s ease forwards;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.8s ease forwards;
}

.animate-fadeInRight {
    animation: fadeInRight 0.8s ease forwards;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* ========== UTILITY CLASSES ========== */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 3rem; }

.d-flex {
    display: flex;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.align-center {
    align-items: center;
}

.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }

/* ========== MODERN ENHANCEMENTS ========== */
.btn {
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.btn:hover {
    transform: translateY(-3px) scale(1.02);
}

.modern-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.feature-icon-orange:hover,
.feature-icon-blue:hover {
    transform: scale(1.1) rotate(5deg);
    transition: all 0.3s ease;
}

.orange-accent {
    position: relative;
    transition: all 0.3s ease;
}

.orange-accent:hover {
    color: #e68900;
}

/* ========== RESPONSIVE DESIGN ========== */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .col-6,
    .col-4,
    .col-8,
    .col-3 {
        flex: 0 0 100%;
        margin-bottom: 20px;
    }
    
    .hero {
        padding: 100px 0 60px;
    }
    
    .section {
        padding: 60px 0;
    }
    
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .btn {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
    
    .feature-icon-orange,
    .feature-icon-blue {
        width: 70px;
        height: 70px;
        font-size: 1.5rem;
    }
    
    .hero-loma::before,
    .hero-loma::after {
        display: none;
    }
    
    .offer-highlight {
        padding: 60px 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        padding: 20px;
    }
    
    .hero {
        padding: 80px 0 40px;
    }
}

/* ========== SCROLL ANIMATIONS ========== */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease;
}

.scroll-reveal.active {
    opacity: 1;
    transform: translateY(0);
}

/* ========== SPECIAL COMPONENTS ========== */
.feature-box {
    text-align: center;
    padding: 40px 20px;
    transition: all 0.3s ease;
}

.feature-box:hover {
    transform: translateY(-5px);
}

.stats-number {
    font-size: 3rem;
    font-weight: 800;
    color: #ff9500;
    display: block;
}

.testimonial {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    position: relative;
}

.testimonial::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: #667eea;
    font-family: serif;
}

.footer {
    background: #2c3e50;
    color: white;
    padding: 50px 0 20px;
}

.footer-link {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: white;
} 