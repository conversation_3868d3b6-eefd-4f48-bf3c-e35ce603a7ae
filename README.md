# Loma Bag Landing Page

Dự án landing page chuyên nghiệp cho xưởng sản xuất túi vải in logo "Loma Bag".

## 🚀 Tính Năng

- **Responsive Design**: Tối ưu cho mọi thiết bị (desktop, tablet, mobile)
- **CSS Framework Chung**: <PERSON><PERSON> thống CSS có thể tái sử dụng cho nhiều landing page
- **JavaScript Framework**: Bộ công cụ JS đầy đủ với animations, form validation, scroll effects
- **SEO Optimized**: Tối ưu SEO với meta tags, structured data
- **High Conversion**: Thiết kế tập trung vào chuyển đổi cao
- **Modern UI/UX**: <PERSON>ia<PERSON> diện hiện đại, trải nghiệm người dùng tốt

## 📁 Cấu Trúc Dự Án

```
landing-page/
├── index.html              # Landing page chính cho Loma Bag
├── assets/
│   ├── css/
│   │   └── common.css      # CSS framework chung
│   └── js/
│       └── common.js       # JavaScript framework chung
└── README.md              # Hướng dẫn sử dụng
```

## 🎨 CSS Framework Chung

### Các Components Có Sẵn:
- **Layout**: Container, Grid system, Flexbox utilities
- **Typography**: Headings, paragraphs, text utilities
- **Buttons**: Primary, secondary, success với hover effects
- **Cards**: Card components với shadows và hover animations
- **Forms**: Form inputs, validation styles
- **Navigation**: Header, navbar với scroll effects
- **Animations**: Fade in, slide effects, scroll reveals

### Cách Sử Dụng:
```html
<!-- Button examples -->
<button class="btn btn-primary">Primary Button</button>
<button class="btn btn-secondary">Secondary Button</button>

<!-- Card example -->
<div class="card">
    <div class="card-icon">
        <i class="fas fa-star"></i>
    </div>
    <h4>Card Title</h4>
    <p>Card content...</p>
</div>

<!-- Grid system -->
<div class="row">
    <div class="col col-6">Half width</div>
    <div class="col col-6">Half width</div>
</div>
```

## ⚡ JavaScript Framework

### Tính Năng JavaScript:
- **Scroll Reveal**: Animations khi scroll
- **Form Validation**: Validate form tự động
- **Smooth Scrolling**: Cuộn mượt giữa các sections
- **Counter Animation**: Đếm số liệu thống kê
- **Modal System**: Popup/modal functionality
- **Mobile Menu**: Menu responsive cho mobile
- **Notification System**: Thông báo toast
- **Lazy Loading**: Lazy load images

### Cách Sử dụng:
```html
<!-- Scroll reveal animation -->
<div class="scroll-reveal">Content sẽ fade in khi scroll</div>

<!-- Counter animation -->
<span class="stats-number" data-target="1000">0</span>

<!-- Modal trigger -->
<button data-modal="myModal">Open Modal</button>
```

## 🛠️ Hướng Dẫn Sử Dụng

### 1. Tạo Landing Page Mới:
1. Copy file `index.html` và đổi tên theo dự án mới
2. Cập nhật title, meta description, content
3. Thay đổi màu sắc trong CSS variables nếu cần:
```css
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
    --accent-color: #your-color;
}
```

### 2. Customize Components:
- Sử dụng các class có sẵn trong `common.css`
- Override styles cho từng project cụ thể
- Thêm custom CSS trong `<style>` tag hoặc file riêng

### 3. Thêm Nội Dung:
- Hero section: Thay đổi tiêu đề, mô tả, CTA buttons
- Sections: Thêm/bớt sections theo nhu cầu
- Forms: Customize form fields và validation
- Contact info: Cập nhật thông tin liên hệ, social links

## 📱 Responsive Breakpoints

```css
/* Tablet */
@media (max-width: 768px) { }

/* Mobile */
@media (max-width: 480px) { }
```

## 🎯 Landing Page Loma Bag

### Sections Được Tối Ưu:
1. **Hero Section**: CTA mạnh mẽ, value proposition rõ ràng
2. **Stats Section**: Social proof với số liệu ấn tượng
3. **About Section**: Giải thích tại sao chọn Loma Bag
4. **Products Section**: Showcase các loại túi với giá cả
5. **Offer Section**: Ưu đãi đặc biệt (túi mẫu miễn phí)
6. **Quote Form**: Form báo giá với validation
7. **Contact Section**: Đa dạng kênh liên hệ
8. **Footer**: Thông tin công ty và liên kết

### Tối Ưu Chuyển Đổi:
- **Scarcity**: Offer giới hạn (chỉ ship 30k)
- **Social Proof**: Testimonials và số liệu
- **Multiple CTAs**: Nhiều nút hành động ở vị trí chiến lược
- **Trust Signals**: Cam kết chất lượng, bảo hành
- **Easy Contact**: WhatsApp floating button, form đơn giản

## 🚀 Deploy

1. Upload tất cả files lên web server
2. Đảm bảo đường dẫn CSS/JS đúng
3. Test trên nhiều thiết bị khác nhau
4. Cập nhật Google Analytics (nếu có)

## 📞 Tùy Chỉnh Thông Tin Liên Hệ

Cập nhật các thông tin sau trong `index.html`:
- Số điện thoại WhatsApp: Thay `84123456789`
- Email: Thay `<EMAIL>`
- Địa chỉ: Cập nhật địa chỉ thực tế
- Social media links

## 🎨 Customization Tips

### Thay Đổi Màu Sắc:
```css
:root {
    --primary-color: #2c5aa0;   /* Màu chính */
    --secondary-color: #f39c12; /* Màu phụ */
    --accent-color: #27ae60;    /* Màu nhấn */
}
```

### Thêm Fonts:
```html
<link href="https://fonts.googleapis.com/css2?family=YourFont:wght@400;600;700&display=swap" rel="stylesheet">
```

### Custom Animations:
```css
@keyframes yourAnimation {
    from { opacity: 0; }
    to { opacity: 1; }
}

.your-element {
    animation: yourAnimation 1s ease;
}
```

## 🔧 Troubleshooting

**CSS không load:**
- Kiểm tra đường dẫn file CSS
- Đảm bảo file `common.css` tồn tại

**JavaScript không hoạt động:**
- Kiểm tra console có lỗi không
- Đảm bảo file `common.js` được load sau DOM

**Form không submit:**
- Kiểm tra form validation
- Cập nhật form action URL nếu cần

## 📈 Tối Ưu SEO

- Cập nhật title, meta description cho từng page
- Thêm schema markup cho business
- Optimize images với alt tags
- Sử dụng heading tags đúng cấu trúc (h1, h2, h3...)

---

**Liên Hệ Phát Triển:**
- Framework này được thiết kế để tái sử dụng cho nhiều landing page khác nhau
- Có thể dễ dàng customize theo từng ngành nghề cụ thể
- Hỗ trợ phát triển thêm tính năng theo yêu cầu 